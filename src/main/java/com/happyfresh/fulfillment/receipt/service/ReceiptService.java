package com.happyfresh.fulfillment.receipt.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.mapper.ReceiptMapper;
import com.happyfresh.fulfillment.batch.service.FileUploaderService;
import com.happyfresh.fulfillment.common.exception.type.InvalidReceiptException;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.creditCard.service.CreditCardService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.receipt.form.ReceiptFinalizeForm;
import com.happyfresh.fulfillment.receipt.presenter.PendingReceiptPresenter;
import com.happyfresh.fulfillment.repository.ReceiptImageRepository;
import com.happyfresh.fulfillment.repository.ReceiptRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ReceiptService {

    private static final String EXCEPTION_MESSAGE_NO_RECEIPT_FOUND = "No receipt with that receipt number";

    private final Logger logger = LoggerFactory.getLogger(ReceiptService.class);

    @Autowired
    private ReceiptRepository receiptRepository;

    @Autowired
    private ReceiptImageRepository receiptImageRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ReceiptMapper receiptMapper;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private FileUploaderService fileUploaderService;

    @Autowired
    private JedisCacheService jedisCacheService;

    @Autowired
    CreditCardService creditCardService;

    public List<PendingReceiptPresenter> getPendingReceiptCached(User user) {
        String cacheKey = getCacheKey(user.getId());
        Optional<String> cacheResult = jedisCacheService.get(cacheKey);
        if (cacheResult.isPresent()) {
            try {
                String cacheValue = cacheResult.get();
                return mapper.readValue(cacheValue, new TypeReference<List<PendingReceiptPresenter>>() {});
            } catch (Exception e) {
                logger.error("[PendingReceipt] failed parse cache value.", e);
                return getPresentersAndFillToCache(user);
            }
        }
        return getPresentersAndFillToCache(user);
    }

    @Transactional(readOnly = true)
    public List<Receipt> getPendingReceipt(User user) {
        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1L);
        return receiptRepository.findAllPendingReceipts(user, oneWeekAgo);
    }

    @Transactional(readOnly = true)
    public List<ReceiptImage> getReceiptImages(String shipmentNumber, Long receiptId) {
        return receiptImageRepository.findAllReceiptImages(shipmentNumber, receiptId);
    }

    @Transactional
    public ReceiptImage addReceiptImage(String shipmentNumber, Long receiptId, MultipartFile attachment) throws Exception {
        Receipt receipt = receiptRepository.findReceiptById(shipmentNumber, receiptId);
        if (receipt == null)
            throw new InvalidReceiptException(EXCEPTION_MESSAGE_NO_RECEIPT_FOUND);

        String url = fileUploaderService.uploadReceipt(attachment);
        ReceiptImage receiptImage = new ReceiptImage();
        receiptImage.setUrl(url);
        receiptImage.setReceipt(receipt);

        return receiptImageRepository.save(receiptImage);
    }

    @Transactional
    public void deleteReceiptImage(String shipmentNumber, Long receiptId, Long receiptImageId) {
        Receipt receipt = receiptRepository.findReceiptById(shipmentNumber, receiptId);
        if (receipt == null)
            throw new InvalidReceiptException(EXCEPTION_MESSAGE_NO_RECEIPT_FOUND);

        Optional<ReceiptImage> deletedImage = receipt.getReceiptImages().stream().filter(image -> image.getId().longValue() == receiptImageId.longValue()).findFirst();
        if (deletedImage.isPresent()) {
            ReceiptImage deletedReceiptImage = deletedImage.get();
            receipt.getReceiptImages().remove(deletedReceiptImage);
            receiptRepository.save(receipt);
        }
    }

    @Transactional
    public Receipt finalizeReceipt(String shipmentNumber, Long receiptId, ReceiptFinalizeForm receiptFinalizeForm) throws Exception {
        Receipt receipt = receiptRepository.findReceiptById(shipmentNumber, receiptId);
        if (receipt == null)
            throw new InvalidReceiptException(EXCEPTION_MESSAGE_NO_RECEIPT_FOUND);

        receipt.setTotal(BigDecimal.valueOf(receiptFinalizeForm.getTotal()));

        Double tax = receiptFinalizeForm.getTax();
        if (receipt.getShipment().getSlot().getStockLocation().getState().getCountry().isTaxRequired() && tax == null) {
            throw new InvalidReceiptException("Tax required for this receipt");
        }

        if (tax != null) {
            receipt.setTax(BigDecimal.valueOf(tax));
        }
        receipt.setCompleted(true);
        return receiptRepository.save(receipt);
    }

    private List<PendingReceiptPresenter> getPresentersAndFillToCache(User user) {
        List<PendingReceiptPresenter> presenters = getPendingReceiptPresenter(user);

        try {
            String key = getCacheKey(user.getId());
            String value = mapper.writeValueAsString(presenters);
            jedisCacheService.setWithExpiry(key, value, 1800);
        } catch (JsonProcessingException e) {
            logger.error("[PendingReceipt] failed persist cache value.", e);
        }

        return presenters;
    }

    private String getCacheKey(Long userId) {
        return "V2:PENDING_RECEIPT:user_id:" + userId;
    }

    private List<PendingReceiptPresenter> getPendingReceiptPresenter(User user) {
        List<Receipt> receipts = getPendingReceipt(user);
        return receipts.stream()
                .map(receiptMapper::receiptToPendingReceiptPresenter)
                .collect(Collectors.toList());
    }

    public void invalidateCache(Receipt receipt) {
        if (!receipt.getShipment().getJobs().isEmpty()) {
            receipt.getShipment().getShoppingOrRangerJob().ifPresent(this::deleteReceiptCache);
        }
    }

    private void deleteReceiptCache(Job job) {
        Long userId = job.getBatch().getUser().getId();
        String cacheKey = getCacheKey(userId);
        jedisCacheService.del(cacheKey);
    }

}
