package com.happyfresh.fulfillment.batch.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.admin.mapper.AdminBatchMapper;
import com.happyfresh.fulfillment.batch.bean.autoassignment.Assignment;
import com.happyfresh.fulfillment.batch.bean.autoassignment.NearestBatch;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.common.service.WebhookCustomPublisherService;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationEventPublisherService;
import com.happyfresh.fulfillment.user.service.AgentService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;

@Service
public class DriverAutoAssignmentService extends BaseAutoAssignmentService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchAvailabilityService batchAvailabilityService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private KafkaMessage kafkaMessage;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserService userService;

    @Autowired
    private SlotOptimizationEventPublisherService slotOptimizationEventPublisher;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private WebhookCustomPublisherService webhookCustomPublisherService;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    @Autowired
    private AdminBatchMapper adminBatchMapper;

    private static final String SERVICE_CONTEXT = "DriverAutoAssignment";

    public DriverAutoAssignmentService() {
        super(SERVICE_CONTEXT);
    }

    @Transactional
    public void handleEvent(SlotOptimizationEvent event) {
        Shift shift = getShift(event);
        if (shift == null || !shift.getStockLocation().getCluster().isEnableDriverAutoAssignment()) {
            return;
        }

        List<StockLocation> stockLocations = new ArrayList<>();
        Cluster cluster = shift.getStockLocation().getCluster();
        if (cluster == null) {
            stockLocations.add(shift.getStockLocation());
        } else {
            stockLocations = cluster.getStockLocations();
        }

        // get available batch
        List<Batch> availableBatches = batchAvailabilityService.getUnassignedDriverBatchesByStockLocations(stockLocations);
        // get idle agent
        List<Agent> idleAgents = agentService.getAvailableDriverAgentsByStockLocations(stockLocations);

        logStart(idleAgents, availableBatches, event.getAutoAssignmentTriggerEvent(),
                "[{}] Start shift_id: {}, stock_location_id: {}, agents count: {}, batches count: {}",
                SERVICE_CONTEXT,
                shift.getId(),
                shift.getStockLocation().getId(),
                idleAgents.size(),
                availableBatches.size()
        );

        if (idleAgents.isEmpty() || availableBatches.isEmpty()) {
            return;
        }

        List<Batch> saveBatches = assignBatch(shift, availableBatches, idleAgents);
        List<Assignment> assignments = saveBatches.stream().map(batch -> new Assignment(batch, batch.getUser().getAgent()))
                .collect(Collectors.toList());
        afterAssignment(assignments, idleAgents, availableBatches, event.getAutoAssignmentTriggerEvent(),
                "[{}] Finish shift_id: {}, stock_location_id: {}, agents count: {}, batches count: {}, assignments count: {}",
                SERVICE_CONTEXT,
                shift.getId(),
                shift.getStockLocation().getId(),
                idleAgents.size(),
                availableBatches.size(),
                assignments.size()
        );
        sendStratoDeliveryBooked(shift.getStockLocation(), saveBatches);
    }

    @Override
    public void afterAssignment(List<Assignment> assignments, List<Agent> agents, List<Batch> batches,
                                SlotOptimizationEvent.AutoAssignmentTriggerEvent triggerEvent,
                                String messageFormat, Object... arguments) {
        super.afterAssignment(assignments, agents, batches, triggerEvent, messageFormat, arguments);

        if (!assignments.isEmpty()) {
            Tenant tenant = assignments.get(0).getBatch().getTenant();
            Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
            User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);

            for (Assignment assignment : assignments) {
                // Add logging
                Batch batch = assignment.getBatch();
                for (Shipment shipment : batch.getAllShipments()) {
                    bookingLogService.logAssignment(batch, shipment, batch.getUser(), BookingLog.State.ASSIGNED, BookingLog.AssignedBy.AUTO_ASSIGNED);
                }
            }

            SecurityContextHolder.clearContext();
        }
    }

    private Shift getShift(SlotOptimizationEvent event) {
        if (!event.getDeliveryShiftIdAsText().isEmpty()) {
            Long shiftId = Long.parseLong(event.getDeliveryShiftIdAsText());
            return shiftRepository.findById(shiftId).orElse(null);
        }

        return null;
    }

    private List<Batch> assignBatch(Shift shift, List<Batch> availableBatches, List<Agent> idleAgents) {
        List<Batch> saveBatches = new ArrayList<>();
        List<Integer> assignedVehicles = new ArrayList<>();

        List<Long> stockLocationIds = new ArrayList<>();
        Cluster cluster = shift.getStockLocation().getCluster();
        if (cluster == null) {
            stockLocationIds.add(shift.getStockLocation().getId());
        } else {
            stockLocationIds = cluster.getStockLocations().stream().map(StockLocation::getId).collect(Collectors.toList());
        }
        Map<Integer, NearestBatch> nearestBatches = getNearestBatchInStockLocationIds(stockLocationIds);
        Map<Long, Agent> mIdleUserIdAgents = idleAgents.stream().collect(Collectors.toMap(
                agent -> agent.getUser().getId(), agent -> agent
        ));
        List<Agent> processedList = new ArrayList<>(idleAgents);

        for (Batch batch : availableBatches) {
            if (processedList.isEmpty()){
                break;
            }

            if (!assignedVehicles.contains(batch.getVehicle())) {
                Agent agentCandidate = new Agent();
                if (nearestBatches.containsKey(batch.getVehicle()) && mIdleUserIdAgents.containsKey(nearestBatches.get(batch.getVehicle()).getUserId())) {
                    agentCandidate = mIdleUserIdAgents.get(nearestBatches.get(batch.getVehicle()).getUserId());
                } else {
                    Optional<Agent> randomAgent = processedList.stream().findFirst();
                    if (randomAgent.isPresent()) {
                        agentCandidate = randomAgent.get();
                    }
                }

                assignedVehicles.add(batch.getVehicle());
                batch.setUser(agentCandidate.getUser());
                batch.markAsAutoAssigned();
                saveBatches.add(batch);
                checkRangerBatchAndSetJobToStarted(batch);
                mIdleUserIdAgents.remove(agentCandidate.getUser().getId());
                processedList.remove(agentCandidate);
            }
        }

        return batchRepository.saveAll(saveBatches);
    }

    private void checkRangerBatchAndSetJobToStarted(Batch batch) {
        if (batch.getType().equals(Batch.Type.RANGER)) {
            List<Job> initialJobs = batch.getJobs().stream().filter(job -> job.getState().equals(Job.State.INITIAL)).collect(Collectors.toList());
            if (!initialJobs.isEmpty()) {
                initialJobs.forEach(job -> {
                    job.setState(Job.State.STARTED);
                    jobRepository.save(job);
                    webhookCustomPublisherService.publishWebhookForBatch(WebhookType.START_BATCH, batch);
                });
            }
        }
    }

    private Map<Integer, NearestBatch> getNearestBatchInShift(Long shiftId) {
        Map<Integer, NearestBatch> results = new HashMap<>();
        List<Integer> batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        List<Object[]> latestBatches = batchRepository.getLatestBatchWithFinalStateByShiftId(shiftId, batchTypesValue, Job.getInactiveStates());

        for (Object[] batch : latestBatches) {
            Integer vehicle = (Integer) batch[0];
            Long batchId = ((BigInteger) batch[1]).longValue();
            Timestamp batchEndTime = (Timestamp) batch[2];
            Long userId = ((BigInteger) batch[3]).longValue();
            NearestBatch nearestBatch = new NearestBatch(batchId,
                    batchEndTime.toLocalDateTime(), userId);
            results.put(vehicle, nearestBatch);
        }

        return results;
    }

    private Map<Integer, NearestBatch> getNearestBatchInStockLocationIds(List<Long> stockLocationIds) {
        Map<Integer, NearestBatch> results = new HashMap<>();
        List<Integer> batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        List<Object[]> latestBatches = batchRepository.getLatestBatchWithFinalStateByStockLocationIds(stockLocationIds, batchTypesValue, Job.getInactiveStates());

        for (Object[] batch : latestBatches) {
            Integer vehicle = (Integer) batch[0];
            Long batchId = ((BigInteger) batch[1]).longValue();
            Timestamp batchEndTime = (Timestamp) batch[2];
            Long userId = ((BigInteger) batch[3]).longValue();
            NearestBatch nearestBatch = new NearestBatch(batchId,
                    batchEndTime.toLocalDateTime(), userId);
            results.put(vehicle, nearestBatch);
        }

        return results;
    }

    @Override
    public void sendPushNotificationForNewAssignment(List<Assignment> assignments) {
        for (Assignment assignment : assignments) {
            Batch batch = assignment.getBatch();
            Long userId = batch.getUser().getId();
            Long tenantId = batch.getTenant().getId();
            notificationService.sendPushNotificationForNewDeliveryOrRangerAssignment(userId, tenantId);
        }
    }

    @Async
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public void publishAutoAssignmentEvent(String clusterId, String deliveryShiftId, SlotOptimizationEvent.AutoAssignmentTriggerEvent triggerEvent) {
        Cluster cluster = clusterRepository.findById(Long.valueOf(clusterId)).orElse(null);
        Shift shift = shiftRepository.findById(Long.parseLong(deliveryShiftId)).orElse(null);

        if (shift != null && cluster != null && cluster.isEnableDriverAutoAssignment()) {
            try {
                String eventType = SlotOptimizationEvent.AUTO_ASSIGNMENT_DRIVER_EVENT;
                SlotOptimizationEvent event = createSlotOptimizationEvent("", eventType, "", shift.getId().toString(), triggerEvent);
                boolean shouldDeduplicateEvent = cluster.getTenant().isEnableSlotOptimizationEventDeduplication();

                slotOptimizationEventPublisher.publish(clusterId, event, shouldDeduplicateEvent);
            } catch (JsonProcessingException e) {
                logger.error("[DriverAutoAssignmentService] Failed create auto assignment kafka message for cluster id {} or shift id {}",
                        clusterId, shift.getId());
            }
        }
    }

    private void sendStratoDeliveryBooked(StockLocation stockLocation, List<Batch> batches) {
        if (!stockLocation.isFulfilledByStrato())
            return;

        for (Batch deliveryBatch : batches) {
            if (deliveryBatch.getType().equals(Batch.Type.DELIVERY) && deliveryBatch.getUser() != null) {
                stratoWebhookService.sendDeliveryBookedWebhookByBatch(adminBatchMapper.toAdminBatchPresenter(deliveryBatch));
            }
        }
    }
}
