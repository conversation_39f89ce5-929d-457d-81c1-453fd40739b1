package com.happyfresh.fulfillment.common.messaging.activemq;

import com.happyfresh.fulfillment.common.util.AutowireUtil;
import org.apache.activemq.ScheduledMessage;
import org.springframework.core.env.Environment;
import org.springframework.jms.core.MessageCreator;

import javax.jms.Message;

public class JmsMessage {

    private static Sender sender;

    private long scheduledDelay = 0;

    private String validDestination;

    private String validMessage;

    private Sender.Type senderType = Sender.Type.QUEUE;

    public static JmsMessage instance(String destination, String message) {
        sender = AutowireUtil.getBean(Sender.class);
        Environment environment = AutowireUtil.getBean(Environment.class);
        String validDestination = "fulfillment." + environment.getProperty("activemq.environment") + "." + destination;

        return new JmsMessage(validDestination, message);
    }

    public JmsMessage(String destination, String message) {
        this.validDestination = destination;
        this.validMessage = message;
    }

    public JmsMessage withScheduledDelayInMillis(long scheduledDelay) {
        this.scheduledDelay = scheduledDelay;
        return this;
    }

    public JmsMessage withSenderType(Sender.Type senderType) {
        this.senderType = senderType;
        return this;
    }

    public void send() {
        MessageCreator messageCreator = session -> {
            Message message = session.createTextMessage(validMessage);
            if (this.scheduledDelay > 0)
                message.setLongProperty(ScheduledMessage.AMQ_SCHEDULED_DELAY, this.scheduledDelay);
            return message;
        };

        sender.send(senderType, validDestination, messageCreator);
    }
}
