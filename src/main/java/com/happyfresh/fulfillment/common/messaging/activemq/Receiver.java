package com.happyfresh.fulfillment.common.messaging.activemq;

import com.happyfresh.fulfillment.common.messaging.activemq.type.MessageDestination;
import com.happyfresh.fulfillment.common.service.HyperTrackService;
import com.happyfresh.fulfillment.entity.GrabExpressDelivery;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryMessagingService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryUpdateService;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.RoleRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@ConditionalOnProperty(name = "jms.listener.enabled", matchIfMissing = true)
public class Receiver {

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private HyperTrackService hyperTrackService;

    @Autowired
    private UserService userService;

    @Autowired
    private LalamoveDeliveryUpdateService lalamoveDeliveryUpdateService;

    @Autowired
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    private final Logger LOGGER = LoggerFactory.getLogger(Receiver.class);

    @JmsListener(destination = "fulfillment.${activemq.environment}." +  MessageDestination.GRAB_BOOKING)
    @Transactional
    public void receiveGrabBooking(String message) {
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.getOne(Long.parseLong(message));
        Tenant tenant = grabExpressDelivery.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
        grabExpressService.createGrabExpressBookingAsync(grabExpressDelivery.getId(), authenticationToken);
    }

    @JmsListener(destination = "fulfillment.${activemq.environment}." +  MessageDestination.HYPERTRACK_SYNC)
    @Transactional
    public void syncHypertrackLocation(String message) {
        try {
            JSONArray paramArr = new JSONArray(message);
            JSONObject parameter = (JSONObject) paramArr.get(paramArr.length()-1);
            String deviceId = parameter.getString("device_id");
            Double lat = null;
            Double lon = null;
            if (parameter.getString("type").equalsIgnoreCase("location")) {
                JSONObject data = parameter.getJSONObject("data");
                JSONObject geometry = data.getJSONObject("geometry");
                // lon, lat, altitude according to:
                // https://www.hypertrack.com/docs/references/#references-webhooks-location-payload
                JSONArray coordinates = geometry.getJSONArray("coordinates");
                lon = coordinates.getDouble(0);
                lat = coordinates.getDouble(1);
            }
            if (lat != null && lon != null) {
                hyperTrackService.updateAgentLocation(deviceId, lat, lon);
            }
        }catch (JSONException exception){
            LOGGER.error("JSONException", exception);
        }
    }

    @JmsListener(destination = "fulfillment.${activemq.environment}." +  MessageDestination.LALAMOVE_DELIVERY)
    @Transactional
    public void syncLalamoveOrder(String message) {
        try {
            Long id = Long.parseLong(message);
            updateLalamove(id);
        } catch (NumberFormatException nfe) {
            LOGGER.error("Lalamove receiver error parsing id", nfe);
        }

    }

    private void updateLalamove(Long id) {
        try {
            lalamoveDeliveryMessagingService.logReceivedMessage(id);
            lalamoveDeliveryUpdateService.updateById(id);
        } catch (Exception e) {
            LOGGER.error(String.format("Lalamove receiver error for id %d", id), e);
        }
    }
}
