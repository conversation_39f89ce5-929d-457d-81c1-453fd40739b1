package com.happyfresh.fulfillment.common.messaging.activemq;

import lombok.Getter;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import javax.jms.ConnectionFactory;

@Component
public class JmsTopic {

    @Getter
    private JmsTemplate jmsTemplate;

    public JmsTopic(ConnectionFactory connectionFactory) {
        jmsTemplate = new JmsTemplate(connectionFactory);
        jmsTemplate.setPubSubDomain(true);
    }
}
