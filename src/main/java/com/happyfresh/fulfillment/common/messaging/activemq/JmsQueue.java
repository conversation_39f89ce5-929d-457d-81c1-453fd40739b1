package com.happyfresh.fulfillment.common.messaging.activemq;

import lombok.Getter;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import javax.jms.ConnectionFactory;

@Component
public class JmsQueue {

    @Getter
    private JmsTemplate jmsTemplate;

    public JmsQueue(ConnectionFactory connectionFactory) {
        jmsTemplate = new JmsTemplate(connectionFactory);
        jmsTemplate.setPubSubDomain(false);
    }
}
