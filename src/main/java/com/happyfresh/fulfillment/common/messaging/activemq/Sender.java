package com.happyfresh.fulfillment.common.messaging.activemq;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.MessageCreator;
import org.springframework.stereotype.Component;

@Component
public class Sender {

    public enum Type {
        TOPIC, QUEUE
    }

    @Autowired
    private JmsQueue jmsQueue;

    @Autowired
    private JmsTopic jmsTopic;

    public void send(Type type, String destination, MessageCreator messageCreator) {
        if (type == Type.TOPIC)
            jmsTopic.getJmsTemplate().send(destination, messageCreator);
        else
            jmsQueue.getJmsTemplate().send(destination, messageCreator);
    }
}
