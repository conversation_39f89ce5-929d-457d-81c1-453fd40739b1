package com.happyfresh.fulfillment.creditCard.service;

import com.happyfresh.fulfillment.creditCard.mapper.CreditCardMapper;
import com.happyfresh.fulfillment.creditCard.presenter.CreditCardPresenter;
import com.happyfresh.fulfillment.creditCard.presenter.OyPaymentControlPresenter;
import com.happyfresh.fulfillment.creditCard.presenter.OyRegisterCardPresenter;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.CreditCard;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.oy.form.OyCreditCardPayload;
import com.happyfresh.fulfillment.oy.model.FMSCreditCardRequest;
import com.happyfresh.fulfillment.oy.service.api.OyApiService;
import com.happyfresh.fulfillment.repository.CreditCardRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CreditCardService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CreditCardRepository creditCardRepository;

    @Autowired
    private CreditCardMapper creditCardMapper;

    @Autowired
    private OyApiService oyApiService;

    @Transactional(readOnly = true)
    public List<CreditCardPresenter> getCreditCards() {
        List<CreditCard> creditCards = creditCardRepository.findAll();
        return creditCards.stream()
                .map(creditCardMapper::creditCardToCreditCardPresenter)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public CreditCardPresenter getCreditCardById(long id) {
        CreditCard creditCard = creditCardRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        return creditCardMapper.creditCardToCreditCardPresenter(creditCard);
    }

    @Transactional
    public CreditCard createCreditCard(FMSCreditCardRequest request) throws Exception {
        OyCreditCardPayload payload = new OyCreditCardPayload();
        payload.setHolderName(request.getName());
        payload.setCvv(request.getCvv());
        payload.setExpDate(request.getMonth()+"/"+request.getYear().substring(2));
        payload.setAccountNumber(request.getNumber());
        OyRegisterCardPresenter response = oyApiService.registerCard(payload);
        if (response == null) {
            return null;
        }

        CreditCard creditCard = new CreditCard();
        creditCard.setOyCardId(response.getData().getCardId());
        creditCard.setName(request.getName());
        creditCard.setMonth(request.getMonth());
        creditCard.setYear(request.getYear());
        creditCard.setState(CreditCard.State.DISABLED);
        creditCard.setLastDigits(response.getData().getCardNumberSuffix());

        return creditCardRepository.save(creditCard);
    }

    @Transactional
    public CreditCard assignUserToCreditCard(long creditCardId, long userId) {
        User user = userRepository.findById(userId).orElseThrow(EntityNotFoundException::new);
        CreditCard creditCard = creditCardRepository.findById(creditCardId).orElseThrow(EntityNotFoundException::new);

        // Directly assign the user to the credit card
        creditCard.setUser(user);
        return creditCardRepository.save(creditCard);
    }


    @Transactional
    public CreditCard updateCreditCard(long creditCardId, Long userId, CreditCard.State state) throws Exception {
        CreditCard creditCard = creditCardRepository.findById(creditCardId).orElseThrow(EntityNotFoundException::new);

        // Update the user assignment directly
        if (userId != null && userId > 0) {
            User user = userRepository.findById(userId).orElseThrow(EntityNotFoundException::new);
            CreditCard userCreditCard = creditCardRepository.findByUserId(user.getId());
            if (userCreditCard != null && !userCreditCard.getId().equals(creditCardId)) {
                throw new Exception("User already has a credit card");
            }
            creditCard.setUser(user);
        } else {
            // Remove user assignment
            creditCard.setUser(null);
        }

        if (creditCard.getState() != state) {
            OyPaymentControlPresenter response;
            BigDecimal maxLimit = BigDecimal.ZERO;
            boolean enableMaxLimit = false;
            if (state == CreditCard.State.ENABLED) {
                enableMaxLimit = true;
                maxLimit = CreditCard.DEFAULT_MAX_LIMIT;
            } else if (state == CreditCard.State.AUTOMATIC){
                enableMaxLimit = true;
                maxLimit = BigDecimal.ONE;
            }

            response = oyApiService.paymentControlAdjustMaxLimit(creditCard.getOyCardId(), maxLimit, enableMaxLimit);
            if (response == null || !response.getSuccess()) {
                String additionalMessage = response != null ? response.getReason() : "";
                throw new Exception("Fail to update credit card state in OY " + additionalMessage);
            }

            creditCard.setState(state);
            creditCard = creditCardRepository.save(creditCard);
        }

        return creditCard;
    }

    @Transactional
    public void changeLimitForShopper(Batch batch, boolean startLimit) throws Exception {
        // Find credit card directly by user
        CreditCard creditCard = creditCardRepository.findByUserId(batch.getUser().getId());

        if (creditCard != null && creditCard.getState() == CreditCard.State.AUTOMATIC) {
                BigDecimal maxLimit = BigDecimal.ONE;

                if (startLimit) {
                    maxLimit = batch.getAllShipments().stream()
                            .map(shipment -> shipment.getOrderTotal())
                            .filter(orderTotal -> orderTotal != null)
                            .max(BigDecimal::compareTo)
                            .orElse(BigDecimal.ONE);

                    maxLimit = maxLimit.add(maxLimit.multiply(BigDecimal.valueOf(0.5)));
                    maxLimit = maxLimit.setScale(0, RoundingMode.CEILING);
                }

            OyPaymentControlPresenter response = oyApiService.paymentControlAdjustMaxLimit(creditCard.getOyCardId(), maxLimit, true);
            if (response == null || !response.getSuccess()) {
                String additionalMessage = response != null ? response.getReason() : "";
                throw new Exception("Fail to update limit credit card snd in OY " + additionalMessage);
            }
        }
    }

}
