package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserRepository extends JpaRepository<User, Long> {

    User findByTokenAndTenantId(String token, Long tenantId);

    User findByEmailAndTenantToken(String email, String tenantToken);

    User findByEmail(String email);

    User findByToken(String token);

    User findByRolesContainingAndTenantId(Role role, Long tenantId);

    @Query(value =
            "SELECT DISTINCT users.* " +
                    "FROM hff_user users " +
                    "INNER JOIN hff_user_role user_role ON users.id = user_role.user_id " +
                    "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                    "INNER JOIN hff_user_enabler user_enabler ON user_enabler.user_id = users.id " +
                    "WHERE role.name = :role_name AND user_enabler.enabler = :enabler_name AND users.tenant_id = :tenant_id ", nativeQuery = true)
    List<User> findUsersByRolesTenantAndEnabler(@Param("role_name") String roleName, @Param("tenant_id") Long tenantId, @Param("enabler_name") String enablerName);

    List<User> findAllByRolesContainingAndTenantId(Role role, Long tenantId);

    @Query("SELECT DISTINCT u FROM User u JOIN u.roles r WHERE r IN :roles")
    List<User> findAllByRolesIn(@Param("roles") List<Role> roles);

    Boolean existsByEmail(String email);

}
