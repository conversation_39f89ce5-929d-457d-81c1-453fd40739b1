package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.CreditCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CreditCardRepository extends JpaRepository<CreditCard, Long> {
    List<CreditCard> findByIdIn(List<Long> ids);

    CreditCard findByUserId(Long userId);
}
