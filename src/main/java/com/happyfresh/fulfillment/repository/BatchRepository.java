package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.batch.model.ShoppingBatchEarliestDelivery;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.slot.model.DeliveryVehicleBatchTime;
import com.happyfresh.fulfillment.slot.model.ShoppingVehicleBatchTime;
import com.happyfresh.fulfillment.slot.model.VehicleBatchTime;
import com.happyfresh.fulfillment.slot.model.VehicleBatchTimeBySlot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface BatchRepository extends JpaRepository<Batch, Long> {

    List<Batch> findByType(Batch.Type type);

    List<Batch> findByTypeAndDeliveryType(Batch.Type type, Batch.DeliveryType deliveryType);

    @Query("SELECT batch FROM Batch batch " +
            "LEFT JOIN batch.jobs job " +
            "LEFT JOIN job.shipment shipment " +
            "LEFT JOIN shipment.slot slot " +
            "WHERE batch.id = ?1")
    Batch fetchById(Long id);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "LEFT JOIN FETCH batch.shift shift " +
            "INNER JOIN FETCH batch.jobs job " +
            "INNER JOIN FETCH batch.stockLocation stockLocation " +
            "INNER JOIN FETCH job.shipment shipment " +
            "WHERE shipment.orderNumber = ?1 AND batch.type IN (1,2) ")
    Batch fetchWithDeliveryJobAndStockLocationByOrderNumber(String orderNumber);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
            "batch.type IN (?1) AND " +
            "batch.user_id = ?2 AND " +
            "job.state NOT IN (?3) AND " +
            "batch.start_time > ?4 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllActiveBatches(List<Integer> batchTypes, Long userId, List<Integer> inactiveStates, LocalDateTime minStart);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "WHERE batch.stock_location_id IN (?1) AND shipment.state = 'READY' " +
            "AND batch.user_id IS NULL " +
            "AND batch.start_time <= ?2 AND batch.start_time > ?3 AND batch.shopping_type IS NULL " +
            "AND batch.type = 0 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableShoppingBatches(List<Long> stockLocationIds, LocalDateTime maxStartTime, LocalDateTime minimumStartTime);

    @Query(value = "SELECT " +
                    "new com.happyfresh.fulfillment.batch.model.ShoppingBatchEarliestDelivery(s_batch.id, s_batch.startTime as batchStartTime, s_batch.endTime as batchEndTime, MIN(d_batch.startTime) as earliestDeliveryBatchStartTime, slot.startTime as slotStartTime) " +
                "FROM Batch s_batch " +
                    "LEFT JOIN Job s_job ON s_job.batch.id = s_batch.id " +
                    "LEFT JOIN Shipment shipment ON shipment.id = s_job.shipment.id " +
                    "LEFT JOIN Job d_job " +
                        "ON d_job.shipment.id = shipment.id " +
                        "AND d_job.type = 1 " +
                    "LEFT JOIN Batch d_batch ON d_job.batch.id = d_batch.id " +
                    "INNER JOIN shipment.slot slot " +
                "WHERE " +
                    "s_batch.stockLocation.id = :stockLocationId " +
                    "AND s_batch.type = 0 " +
                    "AND shipment.state = 'READY' " +
                    "AND s_batch.user.id IS NULL " +
                    "AND s_batch.startTime > :minStartTime " +
                    "AND s_batch.startTime <= :maxStartTime " +
                    "AND s_batch.shoppingType IS NULL " +
                "GROUP BY s_batch.id, s_batch.startTime, s_batch.endTime, slot.startTime")
    List<ShoppingBatchEarliestDelivery> findAllShoppingBatchEarliestDelivery(
            @Param("stockLocationId") Long stockLocationId,
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime);

    @Query(value = "SELECT " +
                "hsl.id as stock_location_id, " +
                "s_batch.id as shopping_batch_id, " +
                "MIN(d_batch.start_time) as earliest_delivery_batch_start, " +
                "s_batch.start_time as batch_start, " +
                "s_batch.end_time as batch_end, " +
                "slot.start_time as slot_start_time " +
            "FROM hff_batch s_batch " +
            "INNER JOIN hff_job s_job ON s_job.batch_id = s_batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = s_job.shipment_id " +
            "INNER JOIN hff_job d_job ON d_job.shipment_id = shipment.id AND d_job.type = 1 " +
            "INNER JOIN hff_batch d_batch ON d_job.batch_id = d_batch.id " +
            "INNER JOIN hff_stock_location hsl ON hsl.id = s_batch.stock_location_id " +
            "INNER JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE " +
                "s_batch.type = 0 " +
                "AND s_batch.stock_location_id in :stockLocationIds " +
                "AND s_batch.user_id IS NULL " +
                "AND s_batch.start_time > (NOW() - INTERVAL '1 DAY') " +
                "AND s_batch.start_time <= :currentDayEnd " +
                "AND s_batch.shopping_type IS NULL " +
                "AND shipment.state = 'READY' " +
            "GROUP BY hsl.id, s_batch.id, slot.start_time", nativeQuery = true)
    List<Object[]> findAllShoppingBatchIdsWithEarliestDeliveryTime(
            @Param("stockLocationIds") List<Long> stockLocationIds,
            @Param("currentDayEnd") LocalDateTime currentDayEnd);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "JOIN hff_stock_location stock_location ON stock_location.id = batch.stock_location_id " +
            "JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE batch.stock_location_id IN (?1) AND shipment.state = 'READY' " +
            "AND batch.user_id IS NULL " +
            "AND (slot.start_time - INTERVAL '1 minutes' * stock_location.shopping_batch_notified_offset) <= NOW() " +
            "AND batch.start_time > ?2 AND batch.shopping_type IS NULL " +
            "AND batch.type = 0 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableShoppingBatchesV2(List<Long> stockLocationIds, LocalDateTime minimumStartTime);

    @Query(value = "WITH " +
            "notifiable_stock_locations as (" +
                "SELECT hsl.id FROM hff_stock_location hsl " +
                "INNER JOIN hff_state state ON state.id = hsl.state_id " +
                "WHERE state.country_id = :countryId " +
                    "AND hsl.preferences -> 'enable_pending_job_notification' IS NOT NULL " +
                    "AND hsl.preferences -> 'enable_pending_job_notification' = 'true') " +
            "SELECT " +
                "hsl.id, " +
                "hsl.name, " +
                "COUNT(batch.id) " +
            "FROM hff_batch batch " +
                "INNER JOIN hff_stock_location hsl ON hsl.id = batch.stock_location_id " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "WHERE " +
                "batch.stock_location_id IN (select id from notifiable_stock_locations) " +
                "AND shipment.state = 'READY' " +
                "AND batch.user_id IS NULL " +
                "AND batch.start_time > (NOW() - INTERVAL '7 DAY') " +
                "AND batch.start_time <= (NOW() + (interval '1 MINUTE' * hsl.shopping_batch_notified_offset)) " +
                "AND batch.shopping_type IS NULL " +
                "AND batch.type = 0 " +
            "GROUP BY hsl.id, hsl.name", nativeQuery = true)
    List<Object[]> countAvailableShoppingBatchesInCountryGroupedByStockLocationId(Long countryId);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "WHERE batch.stock_location_id IN (?1) " +
            "AND shipment.state = 'PENDING' " +
            "AND batch.user_id IS NULL " +
            "AND batch.start_time <= ?2 AND batch.start_time > ?3 " +
            "AND batch.shopping_type IS NULL " +
            "AND batch.type = 0 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findUpcomingShoppingBatches(List<Long> stockLocationIds, LocalDateTime maxStartTime, LocalDateTime minStartTime);

    @Query(value = "WITH normal_stock_location as" +
                        "(SELECT stock_location.id FROM hff_stock_location stock_location WHERE type = 0 AND id IN (?1)), " +
                    "special_stock_location as" +
                        "(SELECT stock_location.id FROM hff_stock_location stock_location WHERE type = 1 AND id IN (?1)), " +
                    "next_hour_batches as" +
                        "(SELECT batch.*, MAX(shipment.ship_distance) as ship_distance " +
                                "FROM hff_batch batch " +
                                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                                "WHERE shipment.state = 'READY' AND " +
                                "batch.user_id IS NULL " +
                                "AND batch.start_time <= ?2 " +
                                "AND batch.start_time > ?3 " +
                                "AND batch.type IN (1,2) " +
                                "AND batch.delivery_type <> 2 " +
                                "GROUP BY batch.id), " +
                    "available_batches as" +
                        "(SELECT batch.* FROM next_hour_batches batch " +
                            "INNER JOIN hff_stock_location stock_location ON stock_location.id = batch.stock_location_id " +
                            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                        "WHERE batch.stock_location_id IN " +
                                "(SELECT id FROM normal_stock_location) AND " +
                        "(stock_location.tpl_enabled = 'f' OR " +
                        "stock_location.ship_distance_threshold IS NULL OR" +
                        " batch.ship_distance <= stock_location.ship_distance_threshold OR" +
                        " batch.ship_distance IS NULL) AND" +
                        " (batch.notified_driver_id = ?4 AND" +
                        " batch.driver_notified_at >= ?5 OR " +
                        "batch.driver_notified_at < ?5 OR " +
                        "batch.driver_notified_at IS NULL OR " +
                        "batch.driver_skipped_at IS NOT NULL ) " +
                        "UNION " +
                        "SELECT batch.* FROM next_hour_batches batch " +
                        "WHERE batch.stock_location_id IN " +
                            "(SELECT id FROM special_stock_location)) " +
            "SELECT batch.* FROM available_batches batch " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableDeliveryBatches(List<Long> stockLocationIds, LocalDateTime maxStartTime, LocalDateTime minimumStartTime, Long userId, LocalDateTime abandonedTime);

    @Query(value = "WITH " +
            "normal_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 0 AND id IN :stockLocationIds), " +
            "special_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 1 AND id IN :stockLocationIds), " +
            "shipment_ids_with_shopping_available as ( " +
                "SELECT DISTINCT shipment.id " +
                "FROM hff_shipment shipment " +
                    "INNER JOIN hff_job job ON job.shipment_id = shipment.id  " +
                    "INNER JOIN hff_batch batch ON batch.id = job.batch_id  " +
                "WHERE " +
                    "shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN :stockLocationIds  " +
                    "AND ( " +
                        "batch.type = 2 " + // ranger
                        "OR (batch.type = 0 AND batch.shopping_type IS null)" + // shopping
                    ") " +
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "batches_within_time_range AS ( " +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.id IN (select id from shipment_ids_with_shopping_available) " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type IN (1, 2) " +
                    "AND batch.delivery_type <> 2 " +
                    "AND batch.user_id is NULL ), " +
            "r_batches AS (" +
                "SELECT batch.* FROM batches_within_time_range batch " +
                "WHERE batch.stock_location_id IN (SELECT id FROM special_stock_location)), " +
            "d_batches AS (" +
                "SELECT batch.* FROM batches_within_time_range batch " +
                "WHERE batch.stock_location_id IN (SELECT id FROM normal_stock_location)), " +
            "d_batches_initial_shopping AS ( " +
                "SELECT " +
                    "batch.id as batch_id, " +
                    "COUNT(shopping_job.state = 0 OR NULL) as initial_state_count " +
                "FROM d_batches batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                    "INNER JOIN hff_job shopping_job ON " +
                        "shopping_job.shipment_id = shipment.id " +
                        "AND shopping_job.type = 0 " + // Job.Type = SHOPPING
                "GROUP BY batch.id) " +
            "SELECT * FROM r_batches " +
            "UNION " +
            "SELECT * FROM d_batches " +
            "WHERE id IN (" +
                "SELECT batch_id " +
                "FROM d_batches_initial_shopping _b " +
                "WHERE _b.initial_state_count = 0)", nativeQuery = true)
    List<Batch> findAllAvailableDeliveryBatchesWithNoInitialShoppingJob(@Param("stockLocationIds") List<Long> stockLocationIds,
                                                                        @Param("maxStartTime") LocalDateTime maxStartTime,
                                                                        @Param("minStartTime") LocalDateTime minStartTime);

        @Query(value = "WITH " +
            "normal_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 0 AND id IN :stockLocationIds), " +
            "special_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 1 AND id IN :stockLocationIds), " +
            "shipment_ids_with_upcoming_shopping_batch as ( " +
                "SELECT DISTINCT shipment.id " +
                "FROM hff_shipment shipment " +
                    "INNER JOIN hff_job job ON job.shipment_id = shipment.id  " +
                    "INNER JOIN hff_batch batch ON batch.id = job.batch_id  " +
                "WHERE " +
                    "shipment.state IN ('PENDING', 'READY') " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type = 0 AND batch.shopping_type IS null " +
                    "AND batch.user_id IS NULL " +
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "batches_pending_shipment AS (" +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.state = 'PENDING' " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type IN (1,2) " + // DELIVERY, RANGER
                    "AND batch.delivery_type <> 2 " + // not TPL
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "d_batches AS (" +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.id IN (SELECT id FROM shipment_ids_with_upcoming_shopping_batch) " +
                    "AND shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN (SELECT id FROM normal_stock_location) " +
                    "AND batch.type = 1 " + // DELIVERY
                    "AND batch.delivery_type <> 2), " + // not TPL
            "d_batches_initial_shopping AS ( " +
                "SELECT " +
                    "batch.id as batch_id, " +
                    "COUNT(shopping_job.state = 0 OR NULL) as initial_state_count " +
                "FROM d_batches batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                    "INNER JOIN hff_job shopping_job ON " +
                        "shopping_job.shipment_id = shipment.id " +
                        "AND shopping_job.type = 0 " + // Job.Type = SHOPPING
                "GROUP BY batch.id) " +
            // ------------ main query ------------
            "SELECT * FROM d_batches " +
                "WHERE id IN (" + // Delivery batches with shopping job state still INITIAL
                    "SELECT batch_id FROM d_batches_initial_shopping _b WHERE _b.initial_state_count > 0" +
                ") " +
            "UNION " +
            "SELECT * FROM batches_pending_shipment", nativeQuery = true)
    List<Batch> findUpcomingDeliveryBatches(@Param("stockLocationIds") List<Long> stockLocationIds,
                                            @Param("minStartTime") LocalDateTime minStartTime,
                                            @Param("maxStartTime") LocalDateTime maxStartTime);

        @Query(value = "WITH " +
            "normal_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 0 AND id IN :stockLocationIds), " +
            "shipment_ids_with_upcoming_shopping_batch as ( " +
                "SELECT DISTINCT shipment.id " +
                "FROM hff_shipment shipment " +
                    "INNER JOIN hff_job job ON job.shipment_id = shipment.id  " +
                    "INNER JOIN hff_batch batch ON batch.id = job.batch_id  " +
                "WHERE " +
                    "shipment.state IN ('PENDING', 'READY') " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type = 0 AND batch.shopping_type IS null " + // shopping
                    "AND batch.user_id IS NULL " +
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "batches_pending_shipment AS (" +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.state = 'PENDING' " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type IN (1,2) " + // DELIVERY, RANGER
                    "AND batch.delivery_type = 2 " + // not TPL
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "tpl_batches AS (" +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.id IN (SELECT id FROM shipment_ids_with_upcoming_shopping_batch) " +
                    "AND shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN (SELECT id FROM normal_stock_location) " +
                    "AND batch.type = 1 " + // DELIVERY
                    "AND batch.delivery_type = 2), " + // TPL
            "tpl_batches_initial_shopping AS ( " +
                "SELECT " +
                    "batch.id as batch_id, " +
                    "COUNT(shopping_job.state = 0 OR NULL) as initial_state_count " +
                "FROM tpl_batches batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                    "INNER JOIN hff_job shopping_job ON " +
                        "shopping_job.shipment_id = shipment.id " +
                        "AND shopping_job.type = 0 " + // Job.Type = SHOPPING
                "GROUP BY batch.id) " +
            // ------------ main query ------------
            "SELECT * FROM tpl_batches " +
                "WHERE id IN (" + // TPL batches with shopping job state still INITIAL
                    "SELECT batch_id FROM tpl_batches_initial_shopping _b WHERE _b.initial_state_count > 0" +
                ") " +
            "UNION " +
            "SELECT * FROM batches_pending_shipment", nativeQuery = true)
    List<Batch> findUpcomingTplBatches(@Param("stockLocationIds") List<Long> stockLocationIds,
                                            @Param("minStartTime") LocalDateTime minStartTime,
                                            @Param("maxStartTime") LocalDateTime maxStartTime);

    @Query(value = "WITH normal_stock_location as" +
                    "(SELECT stock_location.id " +
                        "FROM hff_stock_location stock_location WHERE type = 0 AND id IN (?1)), " +
                    "next_hour_batches as" +
                    "(SELECT batch.*, MAX(shipment.ship_distance) AS ship_distance " +
                        "FROM hff_batch batch " +
                            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                        "WHERE shipment.state = 'READY' AND " +
                        "batch.user_id IS NULL AND " +
                        "batch.start_time <= ?2 AND " +
                        "batch.start_time > ?3 AND " +
                        "batch.type IN (1,2) " +
                        "GROUP BY batch.id), " +
                    "available_batches as" +
                        "(SELECT batch.* FROM next_hour_batches batch " +
                        "INNER JOIN hff_stock_location stock_location ON stock_location.id = batch.stock_location_id " +
                        "WHERE batch.stock_location_id IN " +
                            "(SELECT * FROM normal_stock_location) AND " +
                            "(batch.notified_driver_id = ?4 AND " +
                            "batch.driver_notified_at >= ?5 OR " +
                            "batch.driver_notified_at < ?5 OR " +
                            "batch.driver_notified_at IS NULL OR " +
                            "batch.driver_skipped_at IS NOT NULL ) AND " +
                            "(stock_location.tpl_enabled = 't' AND " +
                            "stock_location.ship_distance_threshold IS NOT NULL AND " +
                            "batch.ship_distance > stock_location.ship_distance_threshold)) " +
            "SELECT batch.* FROM available_batches batch " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableDeliveryBatchesForBackOffice(List<Long> stockLocationIds, LocalDateTime maxStartTime, LocalDateTime minimumStartTime, Long userId, LocalDateTime abandonedTime);

    @Query(value = "WITH " +
            "tpl_stock_location AS (" +
                "SELECT sl.id FROM hff_stock_location sl " +
                "WHERE sl.type = 0 " + // SnD store
                    "AND sl.tpl_enabled = 't' " +
                    "AND sl.id IN :stockLocationIds), " +
            "shipment_ids_with_shopping_available as ( " +
                "SELECT DISTINCT shipment.id " +
                "FROM hff_shipment shipment " +
                    "INNER JOIN hff_job job ON job.shipment_id = shipment.id  " +
                    "INNER JOIN hff_batch batch on batch.id = job.batch_id  " +
                "WHERE " +
                    "shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN (SELECT id FROM tpl_stock_location)  " +
                    "AND batch.type = 0 AND batch.shopping_type IS null " + // shopping
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "tpl_batches AS (" +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE shipment.id IN (SELECT id FROM shipment_ids_with_shopping_available) " +
                    "AND batch.stock_location_id IN (SELECT id FROM tpl_stock_location) " +
                    "AND batch.user_id IS NULL " +
                    "AND batch.type = 1 " + // DELIVERY
                    "AND batch.delivery_type = 2), " + // TPL
            "tpl_batches_initial_shopping AS ( " +
                "SELECT " +
                    "batch.id as batch_id, " +
                    "COUNT(shopping_job.state = 0 OR NULL) as initial_state_count " +
                "FROM tpl_batches batch " +
                    "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                    "INNER JOIN hff_job shopping_job ON " +
                        "shopping_job.shipment_id = shipment.id " +
                        "AND shopping_job.type = 0 " + // INITIAL
                "GROUP BY batch.id) " +
            "SELECT * FROM tpl_batches " +
            "WHERE id IN (" +
                "SELECT batch_id " +
                "FROM tpl_batches_initial_shopping _b " +
                "WHERE _b.initial_state_count = 0)", nativeQuery = true)
    List<Batch> findAvailableTplDeliveryBatchesWithNoInitialShoppingJob(@Param("stockLocationIds") List<Long> stockLocationIds,
                                                                        @Param("maxStartTime") LocalDateTime maxStartTime,
                                                                        @Param("minStartTime") LocalDateTime minimumStartTime);

    @Query( "SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.jobSlots jobSlot " +
            "WHERE jobSlot.slot.id = ?1 " +
            "AND batch.type = ?2 " +
            "AND batch.deliveryType = 1 " +
            "AND batch.user IS NULL ")
    List<Batch> findAllUnassignedLongHourBatch(Long slotId, Batch.Type type);

    @Query( "SELECT DISTINCT batch FROM Batch batch " +
            "WHERE batch.type = ?2 " +
            "AND batch.deliveryType = 1 " +
            "AND batch.user.id = ?1 " +
            "ORDER BY batch.endTime")
    List<Batch> findLastAssignedBatch(Long userId, Batch.Type type);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE batch.type = ?1 AND job.state NOT IN (?2) " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findActiveBatchesByType(Batch.Type type, List<Integer> inactiveStates);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "JOIN FETCH batch.jobs job " +
            "JOIN FETCH job.shipment shipment " +
            "WHERE batch.type = ?1 AND batch.user = ?2 AND job.state NOT IN (?3) " +
            "ORDER BY batch.startTime")
    List<Batch> findActiveBatchesByTypeAndUser(Batch.Type type, User user, List<Job.State> inactiveStates);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
           "JOIN FETCH batch.jobs job " +
           "JOIN FETCH job.shipment shipment " +
           "WHERE batch.type IN (?1) AND batch.user = ?2 AND job.state NOT IN (?3) " +
           "ORDER BY batch.startTime")
    List<Batch> findActiveBatchesByTypesAndUser(List<Batch.Type> types, User user, List<Job.State> inactiveStates);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "JOIN FETCH batch.jobs job " +
            "JOIN FETCH job.shipment shipment " +
            "WHERE batch.type IN (?1) AND batch.user.id IN (?2) AND job.state NOT IN (?3) AND batch.startTime >= ?4 " +
            "ORDER BY batch.startTime")
    List<Batch> findActiveBatchesByTypesAndUserIds(List<Batch.Type> types, List<Long> userIds, List<Job.State> inactiveStates, LocalDateTime minStart);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.jobSlots jobSlot " +
            "WHERE jobSlot.slot.id = ?1 " +
            "AND batch.type = ?2 " +
            "AND batch.deliveryType = 1 " +
            "AND batch.user IS NULL ")
    int countUnassignedLongHourBatch(Long slotId, Batch.Type type);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.jobSlots jobSlot " +
            "WHERE jobSlot.slot.id IN (?1) " +
            "AND batch.type = ?2 " +
            "AND batch.deliveryType = 0 " +
            "AND batch.user IS NULL ")
    int countUnassignedBatch(List<Long> slotIds, Batch.Type type);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "WHERE batch.type = ?1 " +
            "AND job.state NOT IN (?2) " +
            "AND batch.stockLocation = ?3 " +
            "AND batch.user IS NOT NULL " +
            "AND batch.startTime >= ?4 " +
            "AND batch.startTime < ?5 ")
    int countOngoingBatch(Batch.Type type, List<Job.State> inactiveStates, StockLocation stockLocation, LocalDateTime startTime, LocalDateTime endTime);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "WHERE batch.type = ?1 " +
            "AND job.state NOT IN (?2) " +
            "AND batch.stockLocation.onDemandCluster = ?3 " +
            "AND batch.user IS NOT NULL " +
            "AND batch.startTime >= ?4 " +
            "AND batch.startTime < ?5 ")
    int countOngoingBatchInSameOnDemandCluster(Batch.Type type, List<Job.State> inactiveStates, OnDemandCluster onDemandCluster, LocalDateTime startTime, LocalDateTime endTime);

    @Query( "SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "WHERE batch.type = ?1 " +
            "AND job.state NOT IN (?2) " +
            "AND batch.stockLocation.onDemandCluster = ?3 " +
            "AND batch.user IS NOT NULL " +
            "AND batch.startTime >= ?4 " +
            "AND batch.startTime < ?5 ")
    List<Batch> getOngoingBatchInSameOnDemandCluster(Batch.Type type, List<Job.State> inactiveStates, OnDemandCluster onDemandCluster, LocalDateTime startTime, LocalDateTime endTime);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.jobSlots jobSlot " +
            "INNER JOIN jobSlot.slot slot " +
            "INNER JOIN slot.stockLocation stockLocation " +
            "WHERE slot.startTime = ?1 " +
            "AND batch.type = 2 " +
            "AND batch.deliveryType <> 2 " +
            "AND stockLocation.cluster.id = ?2 " +
            "AND stockLocation.id <> ?3 " +
            "AND batch.user IS NULL")
    int countBatchRangerInClusterExcludeStoreId(LocalDateTime slotStartTime, Long clusterId, Long excludeStoreId);

    @Query( "SELECT COUNT (DISTINCT batch) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.jobSlots jobSlot " +
            "WHERE jobSlot.slot.id = ?1 ")
    int countBySlotId(Long slotId);

    @Query(value = "SELECT DISTINCT batch.vehicle, batch.end_time FROM (" +
            "   SELECT hff_batch.*, " +
            "       RANK() OVER (PARTITION BY hff_batch.vehicle ORDER BY hff_batch.end_time DESC) rank " +
            "   FROM hff_batch " +
            "   INNER JOIN hff_job on hff_job.batch_id = hff_batch.id " +
            "   INNER JOIN hff_shipment on hff_job.shipment_id  = hff_shipment.id " +
            "   WHERE hff_batch.type = 0 " +
            "   AND hff_batch.shift_id = :shift_id " +
            "   AND hff_shipment.id NOT IN :shipment_ids " +
            "   AND hff_shipment.state = 'READY' " +
            ") batch " +
            "WHERE rank = 1", nativeQuery = true)
    List<Object[]> findAllLatestBatchGroupByVehiclePerShift(@Param("shift_id") Long shiftID, @Param("shipment_ids") List<Long> shipmentIds);

    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.VehicleBatchTime(batch.shift.id, batch.vehicle, MAX(batch.endTime)) FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE " +
                "slot.id IN :slotIds " +
                "AND batch.shift.id IN :shiftIds " +
                "AND batch.type IN :batchTypes " +
                "AND (" +
                    "batch.id IN :unmodifiableBatchIds " +
                    "OR batch.user IS NOT NULL " +
                    "OR batch.startTime < :filterTime " +
                ")" +
            "GROUP BY batch.shift.id, batch.vehicle " +
            "ORDER BY batch.shift.id, batch.vehicle ")
    List<VehicleBatchTime> findAllLatestUnmodifiableVehicleBatch(
            @Param("batchTypes") List<Batch.Type> batchTypes,
            @Param("slotIds") List<Long> slotIds,
            @Param("shiftIds") List<Long> shiftIds,
            @Param("filterTime") LocalDateTime filterTime,
            @Param("unmodifiableBatchIds") List<Long> unmodifiableBatchIds);

    // Find Latest Batch After NOW But Already Started Group by Shift And Vehicle
    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.VehicleBatchTime(batch.shift.id, batch.vehicle, MAX(batch.endTime)) FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE shift.id = :shiftId " +
            "AND batch.type = :batchType " +
            "AND batch.user IS NOT NULL " +
            "AND batch.startTime > :filterTime " +
            "GROUP BY batch.shift.id, batch.vehicle " +
            "ORDER BY batch.shift.id, batch.vehicle ")
    List<VehicleBatchTime> findAllLatestUnmodifiableVehicleBatch(@Param("batchType") Batch.Type batchType, @Param("shiftId") Long shiftId, @Param("filterTime") LocalDateTime filterTime);

    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.VehicleBatchTime(batch.shift.id, batch.vehicle, MAX(batch.endTime)) FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE slot.id IN :slotIds " +
                "AND shift.id IN :shiftIds " +
                "AND batch.type IN :batchTypes " +
                "AND shipment.id NOT IN :excludeShipmentIds " +
            "GROUP BY batch.shift.id, batch.vehicle " +
            "ORDER BY batch.shift.id, batch.vehicle ")
    List<VehicleBatchTime> findAllLatestVehicleBatch(@Param("batchTypes") List<Batch.Type> batchTypes, @Param("slotIds") List<Long> slotIds, @Param("shiftIds") List<Long> shiftIds, @Param("excludeShipmentIds") List<Long> excludeShipmentIds);

    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.VehicleBatchTimeBySlot(batch.shift.id, batch.vehicle, slot.endTime as slotEndTime, MAX(batch.endTime)) FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE slot.id IN :slotIds " +
            "AND shift.id IN :shiftIds " +
            "AND batch.type = :batchType " +
            "AND shipment.id NOT IN :excludeShipmentIds " +
            "GROUP BY batch.shift.id, batch.vehicle, slot.endTime " +
            "ORDER BY batch.shift.id, batch.vehicle, slot.endTime ")
    List<VehicleBatchTimeBySlot> findAllLatestVehicleBatchBySlot(@Param("batchType") Batch.Type batchType, @Param("slotIds") List<Long> slotIds, @Param("shiftIds") List<Long> shiftIds, @Param("excludeShipmentIds") List<Long> excludeShipmentIds);

    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.ShoppingVehicleBatchTime(batch.shift.id, slot.endTime, batch.vehicle, MAX(batch.endTime)) FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE slot.id IN :slotIds " +
            "AND shift.id IN :shiftIds " +
            "AND batch.type = 0 " +
            "AND shipment.id NOT IN :excludeShipmentIds " +
            "GROUP BY batch.shift.id, slot.endTime, batch.vehicle " +
            "ORDER BY batch.shift.id, slot.endTime, batch.vehicle ")
    List<ShoppingVehicleBatchTime> findAllLatestShoppingVehicleBatch(@Param("slotIds") List<Long> slotIds, @Param("shiftIds") List<Long> shiftIds, @Param("excludeShipmentIds") List<Long> excludeShipmentIds);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "WHERE batch.type = :batchType AND batch.startTime >= :checkingStartTime AND batch.vehicle = :vehicle AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) " +
            "ORDER BY batch.startTime")
    List<Batch> findAllBatchesByTypeAndStartTimeAndVehicleAndShift(@Param("batchType") Batch.Type batchType, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("vehicle") Integer vehicle, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE batch.type = :batchType AND batch.startTime >= :checkingStartTime AND batch.vehicle = :vehicle AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) AND slot.id = :slotId AND shipment.state = 'PENDING' " +
            "ORDER BY batch.startTime")
    List<Batch> findAllBatchesByTypeAndStartTimeAndVehicleAndShiftAndSlot(@Param("batchType") Batch.Type batchType, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("vehicle") Integer vehicle, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId, @Param("slotId") Long slotId);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE batch.type = :batchType AND batch.startTime >= :checkingStartTime AND batch.vehicle = :vehicle AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) AND slot.id = :slotId " +
            "ORDER BY batch.startTime")
    List<Batch> findAllBatchesByTypeAndStartTimeAndVehicleAndShiftAndSlotRegardlessShipmentState(@Param("batchType") Batch.Type batchType, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("vehicle") Integer vehicle, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId, @Param("slotId") Long slotId);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE batch.type = :batchType AND batch.startTime >= :checkingStartTime AND batch.vehicle = :vehicle AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) AND slot.startTime <= :slotStartTime " +
            "ORDER BY batch.startTime")
    List<Batch> findAllBatchesByTypeAndStartTimeAndVehicleAndShiftAndSlotRegardlessShipmentStateV2(@Param("batchType") Batch.Type batchType, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("vehicle") Integer vehicle, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId, @Param("slotStartTime") LocalDateTime slotStartTime);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE batch.type IN :batchTypes AND batch.startTime >= :checkingStartTime AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) AND slot.startTime >= :slotStartTime " +
            "ORDER BY batch.startTime")
    List<Batch> findAllNextFollowingBatchesInSameShift(@Param("batchTypes") List<Batch.Type> batchTypes, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId, @Param("slotStartTime") LocalDateTime slotStartTime);

    @Query( "SELECT new com.happyfresh.fulfillment.slot.model.DeliveryVehicleBatchTime(batch.shift.id, batch.vehicle, batch.startTime as batchStartTime, batch.endTime as batchEndTime, slot.startTime as slotStartTime, slot.endTime as slotEndTime, batch.user.id as userId) FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "INNER JOIN shipment.slot slot " +
            "WHERE batch.type IN :batchTypes " +
            "AND batch.endTime >= :checkingStartTime " +
            "AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) " +
            "AND slot.startTime >= :slotStartTime " +
            "ORDER BY batch.startTime ")
    List<DeliveryVehicleBatchTime> findAllVehicleBatchByShiftAndSlot(@Param("batchTypes") List<Batch.Type> batchTypes, @Param("checkingStartTime") LocalDateTime checkingStartTime, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId, @Param("slotStartTime") LocalDateTime slotStartTime);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
            "INNER JOIN batch.shift shift " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "WHERE batch.type = :batchType AND batch.startTime <= :checkingTime AND batch.endTime >= :checkingTime AND batch.vehicle = :vehicle AND shift.id = :shiftId " +
            "AND (0l = :excludedShipmentId OR shipment.id <> :excludedShipmentId) " +
            "ORDER BY batch.endTime DESC")
    List<Batch> findLastOngoingBatchesByTypeAndStartTimeAndVehicleAndShift(@Param("batchType") Batch.Type batchType, @Param("checkingTime") LocalDateTime checkingTime, @Param("vehicle") Integer vehicle, @Param("shiftId") Long shiftId, @Param("excludedShipmentId") Long excludedShipmentId);

    @Query("SELECT DISTINCT batch FROM Batch batch " +
        "LEFT JOIN FETCH batch.shift shift " +
        "INNER JOIN FETCH batch.jobs job " +
        "INNER JOIN FETCH batch.stockLocation stockLocation " +
        "WHERE stockLocation.cluster.id = ?1 " +
        "AND batch.startTime >= ?2 " +
        "AND batch.startTime <= ?3")
    List<Batch> findByClusterAndMinStartTimeAndMaxStartTime(Long clusterId, LocalDateTime minStartTime, LocalDateTime maxStartTime);

    @Query(value = "SELECT COALESCE(MAX(batch.vehicle), 0) FROM hff_batch batch " +
                    "INNER JOIN hff_job on hff_job.batch_id = batch.id " +
                    "INNER JOIN hff_shipment on hff_job.shipment_id  = hff_shipment.id " +
                    "INNER JOIN hff_slot on hff_slot.id  = hff_shipment.slot_id " +
                    "WHERE batch.shift_id = ?1 AND hff_slot.start_time <= ?2 ", nativeQuery = true)
    int getMaxVehicleInSameShift(Long shiftId, LocalDateTime checkingDateTime);

    @Query(value = "SELECT DISTINCT ON (batch.shift_id) batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job on hff_job.batch_id = batch.id " +
            "INNER JOIN hff_shipment on hff_job.shipment_id  = hff_shipment.id AND (0 = :excludedShipmentId OR hff_shipment.id <> :excludedShipmentId) " +
            "WHERE batch.shift_id = :shiftId AND batch.vehicle = :vehicle " +
            "ORDER BY batch.shift_id, batch.vehicle, batch.end_time DESC ", nativeQuery = true)
    Batch getLatestBatchInSameShiftAndVehicle(@Param("shiftId") Long shiftId,@Param("vehicle") int vehicle, @Param("excludedShipmentId") Long excludedShipmentId);

    @Query(value = "SELECT COALESCE(SUM(CASE WHEN hff_batch.start_time > :start_time THEN (EXTRACT(EPOCH FROM (hff_batch.end_time - hff_batch.start_time))/60) ELSE (EXTRACT(EPOCH FROM (hff_batch.end_time - :start_time))/60) END), 0) FROM hff_batch " +
            "JOIN hff_job ON hff_batch.id = hff_job.batch_id " +
            "JOIN hff_shipment ON hff_job.shipment_id = hff_shipment.id " +
            "JOIN hff_slot ON hff_slot.id = hff_shipment.slot_id " +
            "WHERE hff_batch.end_time > :start_time AND hff_batch.end_time < :end_time " +
            "AND hff_batch.type IN (0) AND hff_batch.tpl_type IS NULL " +
            "AND hff_slot.stock_location_id IN (:stock_location_id) ", nativeQuery = true)
    int findShoppingBatchDurationByTypeAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "SELECT COALESCE(SUM(CASE WHEN hff_batch.start_time > :start_time THEN (EXTRACT(EPOCH FROM (hff_batch.end_time - hff_batch.start_time))/60) ELSE (EXTRACT(EPOCH FROM (hff_batch.end_time - :start_time))/60) END), 0) FROM hff_batch " +
            "JOIN hff_job ON hff_batch.id = hff_job.batch_id " +
            "JOIN hff_shipment ON hff_job.shipment_id = hff_shipment.id " +
            "JOIN hff_slot ON hff_slot.id = hff_shipment.slot_id " +
            "WHERE hff_batch.end_time > :start_time AND hff_batch.end_time < :end_time " +
            "AND hff_batch.type IN (1,2) AND hff_batch.tpl_type IS NULL " +
            "AND hff_slot.stock_location_id IN (SELECT id FROM hff_stock_location WHERE cluster_id = :cluster_id) ", nativeQuery = true)
    int findDeliveryBatchDurationByTypeAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("cluster_id") Long custerId);

    @Query(value = "SELECT DISTINCT hff_batch.* FROM hff_batch " +
            "JOIN hff_job ON hff_batch.id = hff_job.batch_id " +
            "JOIN hff_shipment ON hff_job.shipment_id = hff_shipment.id " +
            "JOIN hff_slot ON hff_slot.id = hff_shipment.slot_id " +
            "WHERE hff_batch.end_time > :start_time AND hff_batch.end_time < :end_time " +
            "AND hff_batch.type = 0 AND hff_batch.tpl_type IS NULL " +
            "AND hff_shipment.state <> 'CANCELLED' " +
            "AND hff_slot.stock_location_id IN (:stock_location_id) " +
            "AND hff_slot.start_time >= :start_of_date AND hff_slot.end_time <= :end_time ", nativeQuery = true)
    List<Batch> findShoppingBatchByTypeAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("start_of_date") LocalDateTime startOfDate, @Param("end_time") LocalDateTime endTime, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "SELECT DISTINCT hff_batch.* FROM hff_batch " +
            "JOIN hff_job ON hff_batch.id = hff_job.batch_id " +
            "JOIN hff_shipment ON hff_job.shipment_id = hff_shipment.id " +
            "JOIN hff_slot ON hff_slot.id = hff_shipment.slot_id " +
            "WHERE hff_batch.end_time > :start_time AND hff_batch.start_time < :end_time " +
            "AND hff_batch.type IN (1,2) AND hff_batch.tpl_type IS NULL " +
            "AND hff_shipment.state <> 'CANCELLED' " +
            "AND hff_slot.stock_location_id IN (SELECT id FROM hff_stock_location WHERE cluster_id = :cluster_id) " +
            "AND hff_slot.start_time >= :start_of_date AND hff_slot.end_time <= :end_time " +
            "ORDER BY hff_batch.start_time ", nativeQuery = true)
    List<Batch> findDeliveryBatchByTypeAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("start_of_date") LocalDateTime startOfDate, @Param("end_time") LocalDateTime endTime, @Param("cluster_id") Long custerId);

    @Query(value = "WITH " +
            "normal_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 0 AND id IN :stockLocationIds), " +
            "shipment_ids_with_shopping_available as ( " +
                "SELECT DISTINCT shipment.id " +
                "FROM hff_shipment shipment " +
                "INNER JOIN hff_job job ON job.shipment_id = shipment.id  " +
                "INNER JOIN hff_batch batch ON batch.id = job.batch_id  " +
                "WHERE " +
                    "shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN :stockLocationIds  " +
                    "AND batch.type = 0 AND batch.shopping_type IS null " +
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime), " +
            "batches_within_time_range AS ( " +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.id IN (select id from shipment_ids_with_shopping_available) " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type = 1 " +
                    "AND batch.delivery_type <> 2 " +
                    "AND batch.user_id is NULL ), " +
            "d_batches AS (" +
                "SELECT batch.* FROM batches_within_time_range batch " +
                "WHERE batch.stock_location_id IN (SELECT id FROM normal_stock_location)), " +
            "d_batches_initial_shopping AS ( " +
                "SELECT " +
                    "batch.id as batch_id, " +
                    "COUNT(shopping_job.state = 0 OR NULL) as initial_state_count " +
                "FROM d_batches batch " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "INNER JOIN hff_job shopping_job ON " +
                    "shopping_job.shipment_id = shipment.id " +
                    "AND shopping_job.type = 0 " + // Job.Type = SHOPPING
                "GROUP BY batch.id) " +
            "SELECT DISTINCT * FROM d_batches " +
            "WHERE id IN (" +
                "SELECT batch_id " +
                "FROM d_batches_initial_shopping _b " +
                "WHERE _b.initial_state_count = 0)", nativeQuery = true)
    List<Batch> findAllAvailableRegularDeliveryBatchesWithNoInitialShoppingJob(@Param("stockLocationIds") List<Long> stockLocationIds,
                                                                        @Param("maxStartTime") LocalDateTime maxStartTime,
                                                                        @Param("minStartTime") LocalDateTime minStartTime);

    @Query(value = "WITH " +
            "special_stock_location AS ( " +
                "SELECT id FROM hff_stock_location WHERE type = 1 AND id IN :stockLocationIds), " +
            "batches_within_time_range AS ( " +
                "SELECT batch.* " +
                "FROM hff_batch batch " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
                "WHERE " +
                    "shipment.state = 'READY' " +
                    "AND batch.stock_location_id IN :stockLocationIds " +
                    "AND batch.type = 2 " +
                    "AND batch.delivery_type <> 2 " +
                    "AND batch.start_time > :minStartTime " +
                    "AND batch.start_time <= :maxStartTime " +
                    "AND batch.user_id is NULL ), " +
            "r_batches AS (" +
                "SELECT batch.* FROM batches_within_time_range batch " +
                "WHERE batch.stock_location_id IN (SELECT id FROM special_stock_location)) " +
            "SELECT DISTINCT * FROM r_batches ", nativeQuery = true)
    List<Batch> findAllAvailableRangerDeliveryBatches(@Param("stockLocationIds") List<Long> stockLocationIds,
                                                                        @Param("maxStartTime") LocalDateTime maxStartTime,
                                                                        @Param("minStartTime") LocalDateTime minStartTime);

    @Query(value = "WITH " +
            "shopping_batch AS ( " +
                "SELECT DISTINCT batch.*, shipment.order_number, job.state FROM hff_batch batch  " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id  " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id  " +
                "LEFT JOIN hff_user usr ON usr.id = batch.user_id  " +
                    "WHERE batch.\"type\" = 0 AND " +
                        "shipment.state = 'READY' AND " +
                        "(:orderNumber = '' OR shipment.order_number = :orderNumber) AND " +
                        "(:emailAddress = '' OR usr.email = :emailAddress) " +
            "), " +
            "delivery_ranger_batch AS ( " +
                "SELECT DISTINCT batch.*, shipment.order_number, job.state FROM hff_batch batch  " +
                "INNER JOIN hff_job job ON job.batch_id = batch.id  " +
                "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id  " +
                "LEFT JOIN hff_user usr ON usr.id = batch.user_id  " +
                    "WHERE batch.\"type\" IN (1,2,3,5) AND " +
                    "shipment.state = 'READY' AND " +
                    "(:orderNumber = '' OR shipment.order_number = :orderNumber) AND " +
                    "(:emailAddress = '' OR usr.email = :emailAddress) " +
            ") " +
            "SELECT DISTINCT sb.id, sb.\"type\", sb.delivery_type, sb.user_id, sb.stock_location_id, sb.start_time, sb.end_time, sb.handover_time, sb.tenant_id, sb.created_at, sb.updated_at, sb.created_by, sb.updated_by, sb.driver_notified_at, sb.driver_skipped_at, sb.notified_driver_id, sb.tpl_type, sb.total_distance, sb.shopping_type, sb.shift_id, sb.vehicle, sb.is_switched_to_hf, sb.flags " +
                "FROM shopping_batch sb " +
                "LEFT JOIN delivery_ranger_batch db ON sb.order_number = db.order_number " +
                    "WHERE (sb.state NOT IN (:inactiveStates) AND (db.state NOT IN (:inactiveStates) OR db.state IS NULL)) OR (sb.state IN (:inactiveStates) AND db.state NOT IN (:inactiveStates)) " +
            "UNION  " +
            "SELECT DISTINCT db.id, db.\"type\", db.delivery_type, db.user_id, db.stock_location_id, db.start_time, db.end_time, db.handover_time, db.tenant_id, db.created_at, db.updated_at, db.created_by, db.updated_by, db.driver_notified_at, db.driver_skipped_at, db.notified_driver_id, db.tpl_type, db.total_distance, db.shopping_type, db.shift_id, db.vehicle, db.is_switched_to_hf, db.flags " +
                "FROM delivery_ranger_batch db " +
                "LEFT JOIN shopping_batch sb ON sb.order_number = db.order_number  " +
                    "WHERE sb.state NOT IN (:inactiveStates) OR db.state NOT IN (:inactiveStates)", nativeQuery = true)
    List<Batch> findAllActiveBatchesByOrderNumberAndUserEmail(@Param("orderNumber") String orderNumber,
                                                              @Param("emailAddress") String emailAddress,
                                                              @Param("inactiveStates") List<Integer> inactiveStates);

    @Query(value = "SELECT " +
                "sbatch.id AS shopping_batch_id, " +
                "sbatch.start_time AS batch_start_time, " +
                "sbatch.end_time AS batch_end_time, " +
                "min(dbatch.start_time) AS earliest_delivery_batch_start_time, " +
                "slot.start_time AS slot_start_time " +
            "FROM hff_batch sbatch " +
            "LEFT JOIN hff_job sjob on sbatch.id = sjob.batch_id " +
            "LEFT JOIN hff_shipment shipment on shipment.id = sjob.shipment_id " +
            "LEFT JOIN hff_job djob on djob.shipment_id = shipment.id AND djob.type = 1 " +
            "LEFT JOIN hff_batch dbatch on dbatch.id = djob.batch_id " +
            "INNER JOIN hff_slot slot on slot.id = shipment.slot_id " +
            "WHERE sbatch.stock_location_id = :stockLocationId " +
                "AND (sbatch.flags->'is_auto_assigned' IS NULL OR sbatch.flags->'is_auto_assigned' = 'false') " +
                "AND sbatch.type = 0 " +
                "AND shipment.state = 'READY' " +
                "AND sbatch.user_id IS NULL " +
                "AND sbatch.start_time > :minStartTime " +
                "AND sbatch.start_time <= :maxStartTime " +
            "GROUP BY sbatch.id, sbatch.start_time, sbatch.end_time, slot.start_time " +
            "ORDER BY slot.start_time " +
            "LIMIT :queryLimit", nativeQuery = true)
    List<Object[]> findAllAvailableUnassignedShoppingBatchesByNLimit(
            @Param("stockLocationId") Long stockLocationId,
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime,
            @Param("queryLimit") int queryLimit);

    @Query(value = "SELECT " +
                "dbatch.* " +
            "FROM " +
                "hff_batch dbatch " +
            "LEFT JOIN hff_job djob ON " +
                "dbatch.id = djob.batch_id " +
            "LEFT JOIN hff_shipment shipment ON " +
                "shipment.id = djob.shipment_id " +
            "INNER JOIN hff_slot slot ON " +
                "slot.id = shipment.slot_id " +
            "WHERE " +
                "dbatch.type IN (1, 2) " +
                "AND shipment.state = 'READY' " +
                "AND dbatch.user_id IS NULL " +
                "AND dbatch.start_time > :minStartTime " +
                "AND dbatch.start_time <= :maxStartTime " +
                "AND dbatch.delivery_type <> 2 " +
                "AND dbatch.shift_id = :shiftId " +
                "AND dbatch.vehicle IS NOT NULL " +
            "GROUP BY  dbatch.id, dbatch.start_time, dbatch.end_time, slot.start_time " +
            "ORDER BY dbatch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableUnassignedDriverBatchesByShiftId(
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime,
            @Param("shiftId") Long shiftId);

    @Query(value = "SELECT " +
            "dbatch.* " +
            "FROM " +
            "hff_batch dbatch " +
            "LEFT JOIN hff_job djob ON " +
            "dbatch.id = djob.batch_id " +
            "LEFT JOIN hff_shipment shipment ON " +
            "shipment.id = djob.shipment_id " +
            "INNER JOIN hff_slot slot ON " +
            "slot.id = shipment.slot_id " +
            "WHERE " +
            "dbatch.type IN (1, 2) " +
            "AND shipment.state = 'READY' " +
            "AND dbatch.user_id IS NULL " +
            "AND dbatch.start_time > :minStartTime " +
            "AND dbatch.start_time <= :maxStartTime " +
            "AND dbatch.delivery_type <> 2 " +
            "AND dbatch.stock_location_id IN (:stockLocationIds) " +
            "AND dbatch.vehicle IS NOT NULL " +
            "GROUP BY  dbatch.id, dbatch.start_time, dbatch.end_time, slot.start_time " +
            "ORDER BY dbatch.start_time", nativeQuery = true)
    List<Batch> findAllAvailableUnassignedDriverBatchesByStockLocationIds(
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime,
            @Param("stockLocationIds") List<Long> stockLocationIds);

    @Query(value = "SELECT " +
            "DISTINCT ON (batch.vehicle) batch.vehicle, batch.id, batch.end_time, batch.user_id " +
            "FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
                "batch.shift_id = ?1 " +
                "AND batch.type IN (?2) " +
                "AND (batch.flags->'is_manual_assigned' IS NULL OR batch.flags->'is_manual_assigned' = 'false')" +
                "AND job.state IN (?3) " +
                "AND batch.delivery_type <> 2 " +
            "ORDER BY batch.vehicle, batch.end_time DESC", nativeQuery = true)
    List<Object[]> getLatestBatchWithFinalStateByShiftId(Long shiftId, List<Integer> batchTypesValue, List<Integer> inactiveStates);

    @Query(value = "SELECT " +
            "DISTINCT ON (batch.vehicle) batch.vehicle, batch.id, batch.end_time, batch.user_id " +
            "FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
            "batch.stock_location_id IN (?1) " +
            "AND batch.type IN (?2) " +
            "AND (batch.flags->'is_manual_assigned' IS NULL OR batch.flags->'is_manual_assigned' = 'false')" +
            "AND job.state IN (?3) " +
            "AND batch.delivery_type <> 2 " +
            "ORDER BY batch.vehicle, batch.end_time DESC", nativeQuery = true)
    List<Object[]> getLatestBatchWithFinalStateByStockLocationIds(List<Long> stockLocationIds, List<Integer> batchTypesValue, List<Integer> inactiveStates);


    @Query(value = "SELECT " +
            "CASE WHEN COUNT(batch) > 0 THEN false " +
            "ELSE true END " +
            "FROM Batch batch " +
            "INNER JOIN batch.jobs job " +
            "INNER JOIN job.shipment shipment " +
            "WHERE batch.id = :batchId " +
            "AND shipment.orderNumber != :orderNumber " +
            "AND job.state NOT IN (:inactiveStates) ")
    boolean isTheLastShipmentInBatch(@Param("batchId") Long batchId,
                                     @Param("orderNumber") String orderNumber,
                                     @Param("inactiveStates") List<Job.State> inactiveStates);

    @Query(value = "SELECT sl.cluster_id, batch.shift_id, COUNT(*) as count_unassigned_batch " +
            "FROM hff_batch batch " +
            "JOIN hff_job job ON batch.id = job.batch_id " +
            "JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "JOIN hff_stock_location sl ON batch.stock_location_id = sl.id AND sl.cluster_id IN :clusterIds " +
            "WHERE batch.type IN (1, 2) " +
            "AND shipment.state = 'READY' " +
            "AND batch.user_id IS NULL " +
            "AND batch.delivery_type <> 2 " +
            "AND batch.vehicle IS NOT NULL " +
            "AND batch.start_time > :minStartTime " +
            "AND batch.start_time <= (CASE WHEN :maxStartTime > (NOW() + interval '1 minutes' * sl.shopping_batch_notified_offset) THEN :maxStartTime ELSE (NOW() + interval '1 minutes' * sl.shopping_batch_notified_offset) END) " +
            "GROUP BY sl.cluster_id, batch.shift_id ", nativeQuery = true)
    List<Object[]> findAllAvailableUnassignedDriverBatchesByClusterIds(
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime,
            @Param("clusterIds") List<Long> clusterIds);

    @Query(value = "SELECT " +
            "batch.* " +
            "FROM " +
            "hff_batch batch " +
            "WHERE " +
            "batch.type = ?1 " +
            "AND batch.stock_location_id = ?2 " +
            "AND shift_id IS NOT NULL  " +
            "AND batch.end_time <= ?3 " +
            "AND batch.start_time > ?4 " +
            "ORDER BY batch.end_time DESC LIMIT 1", nativeQuery = true)
    Batch findEarliestBatchEndTimeWithinSlot(int batchType, Long stockLocationId, LocalDateTime slotEndTime, LocalDateTime minimumEndTime);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "JOIN hff_stock_location stock_location ON stock_location.id = batch.stock_location_id " +
            "JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE batch.stock_location_id IN (?1) AND shipment.state = 'READY' " +
            "AND batch.user_id IS NOT NULL " +
            "AND slot.start_time <= NOW() " +
            "AND batch.start_time > ?2 AND batch.shopping_type IS NULL " +
            "AND job.state IN (?3) " +
            "AND batch.type = 0 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllLateShoppingBatches(List<Long> stockLocationIds, LocalDateTime minimumStartTime, List<Integer> ongoingShoppingStates);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "JOIN hff_stock_location stock_location ON stock_location.id = batch.stock_location_id " +
            "JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE batch.stock_location_id IN (?1) AND shipment.state = 'READY' " +
            "AND slot.start_time <= NOW() " +
            "AND batch.start_time > ?2 "+
            "AND job.state IN (?3) " +
            "AND batch.type = 1 " +
            "ORDER BY batch.start_time", nativeQuery = true)
    List<Batch> findAllLateDeliveryBatches(List<Long> stockLocationIds, LocalDateTime minimumStartTime, List<Integer> ongoingStates);

    @Query(value = "SELECT " +
            "batch.* " +
            "FROM " +
            "hff_batch batch " +
            "WHERE " +
            "batch.type = 0 " +
            "AND shift_id = ?1  " +
            "AND batch.vehicle = ?2 " +
            "AND batch.user_id IS NOT NULL " +
            "ORDER BY batch.end_time DESC LIMIT 1", nativeQuery = true)
    Batch findLastShoppingBatchByShopperNo(Long shiftId, int shopperNo);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "INNER JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE " +
            "batch.type = 0 " +
            "AND batch.shift_id = ?1 " +
            "AND batch.user_id IS NULL " +
            "AND slot.start_time >= ?2 ", nativeQuery = true)
    List<Batch> findRearrangableShoppingBatches(Long shiftId, LocalDateTime filterTime);

    @Query(value = "SELECT DISTINCT batch.* FROM hff_batch batch " +
            "INNER JOIN hff_job job ON job.batch_id = batch.id " +
            "INNER JOIN hff_shipment shipment ON shipment.id = job.shipment_id " +
            "INNER JOIN hff_slot slot ON slot.id = shipment.slot_id " +
            "WHERE " +
            "batch.type = 0 " +
            "AND batch.shift_id = ?1 ", nativeQuery = true)
    List<Batch> findAllShoppingBatches(Long shiftId);

    @Query(value = "SELECT hff_batch.*\n" +
            "FROM hff_shipment\n" +
            "JOIN hff_job ON hff_job.shipment_id = hff_shipment.id\n" +
            "JOIN hff_batch ON hff_job.batch_id = hff_batch.id\n" +
            "WHERE hff_job.type IN (1, 2, 5)\n" +
            "AND hff_shipment.order_number = :orderNumber\n" +
            "LIMIT 1;", nativeQuery = true)
    Batch findDeliveryBatchByOrderNumber(@Param("orderNumber") String orderNumber);

    @Query(value = "SELECT hff_batch.*\n" +
            "FROM hff_shipment\n" +
            "JOIN hff_job ON hff_job.shipment_id = hff_shipment.id\n" +
            "JOIN hff_batch ON hff_job.batch_id = hff_batch.id\n" +
            "WHERE hff_job.type IN (0, 4)\n" +
            "AND hff_shipment.order_number = :orderNumber\n" +
            "LIMIT 1;", nativeQuery = true)
    Batch findShoppingBatchByOrderNumber(@Param("orderNumber") String orderNumber);
}