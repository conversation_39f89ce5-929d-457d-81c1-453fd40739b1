package com.happyfresh.fulfillment.entity;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.BaseEntityWithCreateAudit;
import com.happyfresh.fulfillment.common.util.LocaleUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.javers.core.metamodel.annotation.ShallowReference;

import javax.persistence.*;
import javax.validation.constraints.AssertTrue;
import java.math.BigDecimal;
import java.util.*;

@Setter
@Getter
@Entity
@Table(name = "hff_country")
public class Country extends BaseEntityWithCreateAudit {

    @Id
    @SequenceGenerator(name = "hff_country_id_seq", sequenceName = "hff_country_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "hff_country_id_seq")
    private Long id;

    private String name;

    private String isoName;

    private long upperWeightAdjustmentThreshold;

    private long lowerWeightAdjustmentThreshold;

    private boolean requireReceiptNumber;

    private String customerServicePhone;

    private boolean enableChatTranslation;

    private boolean taxRequired = true;

    private Double airDistanceThresholdConfiguration;

    private Double onDemandDeliveryFee;

    @Type(type = "hstore")
    private Map<String, String> preferences;

    @ShallowReference
    @OneToMany(mappedBy = "country")
    private List<State> states;

    @ShallowReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "country", orphanRemoval = true)
    private List<PackagingType> packagingTypes = new ArrayList<>();

    @AssertTrue
    public boolean isLowerWeightThresholdValid() {
        return lowerWeightAdjustmentThreshold <= 0;
    }

    @AssertTrue
    public boolean isUpperWeightThresholdValid() {
        return upperWeightAdjustmentThreshold >= 0;
    }

    public List<RestrictedDeliveryTime> getAlcoholRestrictedDeliveryTimes() {
        List<RestrictedDeliveryTime> alcoholRestrictedDeliveryTimes = Lists.newArrayList();
        Lists.newArrayList(StringUtils.split(this.getPreferences().getOrDefault("alcohol_restricted_delivery_times", StringUtils.EMPTY), "|")).forEach(
                value -> {
                    String[] restrictedTime = StringUtils.split(value, "-");
                    alcoholRestrictedDeliveryTimes.add(
                            new RestrictedDeliveryTime(Integer.valueOf(restrictedTime[0]), Integer.valueOf(restrictedTime[1])));
                }
        );

        return alcoholRestrictedDeliveryTimes;
    }

    public List<String> getRecipientForFailedBookingGrabExpressEmailAlert() {
        List<String> recipientGrabEmailAlert = Lists.newArrayList();
        Lists.newArrayList(StringUtils.split(this.getPreferences().getOrDefault("recipient_for_failed_booking_grab_express_email_alert", StringUtils.EMPTY), "|")).forEach(
                value -> recipientGrabEmailAlert.add(value)
        );

        return recipientGrabEmailAlert;
    }

    // Product dimension threshold - in centimeters
    public int getGrabExpressMaxProductDimension() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_max_product_dimension", "0"));
    }

    // Total order volume threshold - in liters
    public int getGrabExpressMaxDeliveryVolumeInLitre() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_max_delivery_volume", "30"));
    }

    public int getGosendBikeMaxDeliveryVolumeInLitre() {
        return Integer.parseInt(this.getPreferences().getOrDefault("gosend_bike_max_delivery_volume", "175"));
    }

    public int getGosendCarMaxDeliveryVolumeInLitre() {
        return Integer.parseInt(this.getPreferences().getOrDefault("gosend_car_max_delivery_volume", "1000"));
    }

    public double getGrabExpressMaxCodAmount() {
        return Double.parseDouble(this.getPreferences().getOrDefault("grab_express_max_cod_amount", "0.0"));
    }

    public int getGrabExpressCartonBoxHeightInCM() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_carton_box_height", "0"));
    }

    public int getGrabExpressCartonBoxWidthInCM() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_carton_box_width", "0"));
    }

    public int getGrabExpressCartonBoxDepthInCM() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_carton_box_depth", "0"));
    }

    public Boolean getAvoidTollOnDelivery() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("avoid_toll_on_delivery", "true"));
    }

    public int getSlotAvailabilityNumberOfDays() {
        return Integer.parseInt(this.getPreferences().getOrDefault("slot_availability_number_of_days", "3"));
    }

    public int getSlotAvailabilityMaxDaysForward() {
        return Integer.parseInt(this.getPreferences().getOrDefault("slot_availability_max_days_forward", "7"));
    }

    public double getDeliveryGeofenceRadius() {
        return Double.parseDouble(this.getPreferences().getOrDefault("delivery_geofence_radius", "100.0"));
    }

    public Boolean getAllowVAForExpress() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("allow_va_for_express", "true"));
    }

    public Boolean getAllowVAForTPL() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("allow_va_for_tpl", "true"));
    }

    public Double getDayFullThreshold() {
        return Double.parseDouble(this.getPreferences().getOrDefault("day_full_threshold", "100.0"));
    }

    public Integer getUtilizationNumberOfDays() {
        return Integer.parseInt(this.getPreferences().getOrDefault("utilization_number_of_days", "3"));
    }

    public Integer getCsStartWorkingHour() {
        return Integer.parseInt(this.getPreferences().getOrDefault("cs_start_working_hour", "1"));
    }

    public Integer getCsFinishWorkingHour() {
        return Integer.parseInt(this.getPreferences().getOrDefault("cs_finish_working_hour", "12"));
    }

    public Boolean getAllowEWalletForTPL() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("allow_ewallet_for_tpl", "false"));
    }

    public Double getHighDemandThreshold() {
        return Double.parseDouble(this.getPreferences().getOrDefault("high_demand_threshold", "80.0"));
    }

    public Boolean isEnableLalamoveCod() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_lalamove_cod", "false"));
    }

    public Map<String, String> getPreferences() {
        if (preferences == null) {
            preferences = new HashMap<>();
        }
        return preferences;
    }

    @Getter
    public class RestrictedDeliveryTime {
        private int from;
        private int to;

        RestrictedDeliveryTime(int from, int to) {
            this.from = from;
            this.to = to;
        }
    }

    public String getLanguageCode() {
        if (this.getIsoName().equals("ID"))
            return "id";
        Locale locale = LocaleUtil.findLocaleByCountry(this.getIsoName());
        return locale != null ? locale.getLanguage() : "en";
    }

    public BigDecimal getMaxBasketSizeForGosendBike() {
        Double maxBasketSize = Double.parseDouble(this.getPreferences().getOrDefault("max_basket_size_for_gosend_bike", "1000000"));
        return BigDecimal.valueOf(maxBasketSize);
    }

    public BigDecimal getMinBasketSizeForGosendCar() {
        Double maxBasketSize = Double.parseDouble(this.getPreferences().getOrDefault("min_basket_size_for_gosend_car", "2000000"));
        return BigDecimal.valueOf(maxBasketSize);
    }
}
