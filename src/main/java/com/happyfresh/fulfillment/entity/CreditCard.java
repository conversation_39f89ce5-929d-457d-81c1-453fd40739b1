package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;

import java.math.BigDecimal;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "hff_credit_card")
@FilterDef(name = "tenantFilter", parameters = {@ParamDef(name = "tenantId", type = "long")})
@Filter(name = "tenantFilter", condition = "tenant_id = :tenantId")
public class CreditCard extends BaseEntityWithAudit {

    public static final BigDecimal DEFAULT_MAX_LIMIT = BigDecimal.valueOf(2000000);

    public enum State {
        DISABLED(0),
        ENABLED(1),
        AUTOMATIC(2);

        private int value;

        State(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "hff_credit_card_id_seq")
    @SequenceGenerator(name = "hff_credit_card_id_seq", sequenceName = "hff_credit_card_id_seq", allocationSize = 1)
    private Long id;

    private State state;

    private String name;

    @Column(name = "last_digits")
    private String lastDigits;

    private String month;

    private String year;

    @Column(name = "oy_card_id")
    private String oyCardId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
}
