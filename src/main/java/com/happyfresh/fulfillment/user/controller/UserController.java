package com.happyfresh.fulfillment.user.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.annotation.RequestWrapper;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.type.InvalidSndRolesException;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.security.UserPrincipal;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.repository.RoleRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.user.form.AgentShiftForm;
import com.happyfresh.fulfillment.user.form.UserForm;
import com.happyfresh.fulfillment.user.mapper.AgentShiftMapper;
import com.happyfresh.fulfillment.user.mapper.UserMapper;
import com.happyfresh.fulfillment.user.parameter.LocationParameter;
import com.happyfresh.fulfillment.user.presenter.AgentShiftPresenter;
import com.happyfresh.fulfillment.user.presenter.BaseUserPresenter;
import com.happyfresh.fulfillment.user.presenter.UserPresenter;
import com.happyfresh.fulfillment.user.service.AgentService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.persistence.EntityNotFoundException;
import javax.validation.Valid;
import java.security.NoSuchAlgorithmException;
import java.util.*;

import static org.springframework.util.StringUtils.isEmpty;

@RestController
@RequestMapping("/api/users")
public class UserController {

    private final Logger logger = LoggerFactory.getLogger(UserController.class);

    public static final String KEY_PREFIX_USER = "User_";

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AgentService agentService;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private UserService userService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private AgentShiftMapper agentShiftMapper;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Lazy
    @Autowired
    private JedisCacheService cache;

    @Autowired
    private ObjectMapper mapper;

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/shopper_and_ranger")
    @ResponseWrapper(rootName = "user")
    public List<BaseUserPresenter> getShopperAndRanger() {
        String cacheKey = "all_fulfillment_shopper_and_ranger";
        List<BaseUserPresenter> presenters;
        Optional<String> cachedUsers = cache.get(cacheKey);
        List<Role> roles = new ArrayList<>();

        roles.add(roleRepository.findByName(Role.Name.SHOPPER));
        roles.add(roleRepository.findByName(Role.Name.ON_DEMAND_RANGER));
        try {
            if (cachedUsers.isPresent()) {
                String cachedUsersPresenter = cachedUsers.get();
                presenters = mapper.readValue(cachedUsersPresenter, new TypeReference<List<BaseUserPresenter>>() {});
            } else {
                List<User> users = userRepository.findAllByRolesIn(roles);
                presenters = userMapper.usersToBaseUsersPresenters(users);
                cache.setWithExpiry(cacheKey, mapper.writeValueAsString(presenters), 60 * 5);
            }
        } catch (Exception e) {
            logger.error("Failed to get users from cache", e);
            List<User> users = userRepository.findAllByRolesIn(roles);
            presenters = userMapper.usersToBaseUsersPresenters(users);
        }
        return presenters;
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PostMapping
    @ResponseWrapper(rootName = "user")
    public ResponseEntity<UserPresenter> create(@Validated @RequestBody UserForm userForm) throws NoSuchAlgorithmException {
        User user = userService.findOrCreate(userForm);
        UserPresenter userPresenter = userMapper.userToUserPresenter(user);

        if (Boolean.TRUE.equals(user.getIsNewlyCreated()))
            return new ResponseEntity<>(userPresenter, HttpStatus.CREATED);
        return new ResponseEntity<>(userPresenter, HttpStatus.OK);
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping
    @ResponseWrapper(rootName = "user")
    public ResponseEntity<UserPresenter> update(@Validated @RequestBody UserForm userForm) throws EntityNotFoundException, InvalidSndRolesException {
        UserPresenter userPresenter = userMapper.userToUserPresenter(userService.update(userForm));
        return new ResponseEntity<>(userPresenter, HttpStatus.OK);
    }

    @PostMapping(value = "/login")
    @ResponseWrapper(rootName = "user")
    public UserPresenter login() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        UserPrincipal principal = (UserPrincipal) authentication.getPrincipal();
        if (!principal.getUser().getIsActive())
            throw new AccessDeniedException("User is not active");

        if (!(authentication instanceof UsernamePasswordAuthenticationToken))
            throw new AccessDeniedException("User is not authenticated");

        Collection<? extends GrantedAuthority> userAuthorities = authentication.getAuthorities();
        if (isEmpty(userAuthorities.size())) {
            throw new AccessDeniedException("User has no role.");
        }

        List<String> roles = Arrays.asList(Role.Name.SHOPPER.name(), Role.Name.DRIVER.name());
        List<String> userRoles = new ArrayList<>();
        userAuthorities.forEach(role -> userRoles.add(role.getAuthority()));

        if (userRoles.containsAll(roles)) {
            throw new AccessDeniedException("User has role shopper and driver.");
        }

        return userMapper.userToUserPresenter(principal.getUser());
    }

    @PreAuthorize("isAgentAuthenticated()")
    @GetMapping(value = "/me/agent")
    @ResponseWrapper(rootName = "agent")
    public AgentShiftPresenter agent() throws InterruptedException {
        User user = userInfoFetcher.getAuthenticatedUser();

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(KEY_PREFIX_USER + user.getId())) {
                return agentService.getAgentInformation(user);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/me/agent/clock_in")
    @ResponseWrapper(rootName = "agent")
    @RequestWrapper(rootName = "agent")
    public AgentShiftPresenter clockIn(@Valid @RequestBody AgentShiftForm agentShiftForm) throws InterruptedException {
        User user = userInfoFetcher.getAuthenticatedUser();

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(KEY_PREFIX_USER + user.getId())) {
                StockLocation stockLocation = stockLocationRepository.getOne(agentShiftForm.getStockLocationId());
                Agent agent = agentShiftMapper.agentFormToAgent(agentShiftForm, stockLocation);
                return agentService.processClockIn("WORK", user, agent, agentShiftForm.getClockInDuration());
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {

            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/me/agent/clock_out")
    @ResponseWrapper(rootName = "agent")
    public AgentShiftPresenter clockOut() throws InterruptedException {
        return processNonClockIn("FINISH");
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/me/agent/pause")
    @ResponseWrapper(rootName = "agent")
    public AgentShiftPresenter pause() throws InterruptedException {
        throw new BadRequestException("Action is not allowed");
        // return processNonClockIn("PAUSE");
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/me/agent/resume")
    @ResponseWrapper(rootName = "agent")
    public AgentShiftPresenter resume() throws InterruptedException {
        return processNonClockIn("RESUME");
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/location")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void updateLocation(@Valid @RequestBody LocationParameter locationParameter) throws Exception {
        User user = userInfoFetcher.getAuthenticatedUser();

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(KEY_PREFIX_USER + user.getId())) {
                transactionHelper.withNewTransaction(() -> agentService.updateAgentLocation(user.getAgent(), locationParameter.getLat(), locationParameter.getLon()));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    private AgentShiftPresenter processNonClockIn(String state) throws InterruptedException {
        User user = userInfoFetcher.getAuthenticatedUser();

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(KEY_PREFIX_USER + user.getId())) {
                return agentService.processClockIn(state, user, user.getAgent(), -1);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }
}
