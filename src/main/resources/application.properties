server.port=${PORT:8080}

# Max worker threads
server.undertow.worker-threads=${FSRV_MAX_THREADS:}

# Max DB connection pool
spring.datasource.hikari.maximumPoolSize=${FSRV_MAX_DB_POOL_SIZE:10}

# Logging Configuration Variables
logging.config=classpath:log4j2-${FSRV_LOGGING_PROFILE:development}.xml
logz.io.token=${FSRV_LOGZ_IO_TOKEN:dlVKTjTAdbrdepqtKgcCUdwgEmPBBSdi}
sentry.environment=${SENTRY_ENVIRONMENT:local}
sentry.stacktrace.app.packages=${SENTRY_STACKTRACE_APP_PACKAGES:}
sentry.dsn=${SENTRY_DSN:}

# Redis Configuration Variables
redis.url=${FSRV_REDIS_URL:}
redis.password=${FSRV_REDIS_PASSWORD:}
redis.namespace=${FSRV_REDIS_NAMESPACE:}

# Data Migration Configuration Variables
spring.flyway.baseline-on-migrate=true
spring.flyway.sql-migration-prefix=v
spring.flyway.sql-migration-suffixes=.sql
spring.flyway.out-of-order=true

# Elasticsearch Configuration Variables #
spring.elasticsearch.jest.uris=${FSRV_ES_CLUSTER_NODES:}
spring.elasticsearch.jest.prefix=${FSRV_ES_PREFIX:}
spring.elasticsearch.jest.username=${FSRV_ES_USERNAME:}
spring.elasticsearch.jest.password=${FSRV_ES_PASSWORD:}

# Data Source Configuration Variables #
spring.datasource.url=${FSRV_DATABASE_URL_KEY:*********************************************************************}
spring.jpa.properties.hibernate.default_schema=${FSRV_JPA_DEFAULT_SCHEMA:public}
spring.jpa.show-sql=false

# File size limit
#multipart.maxFileSize = 10Mb
spring.servlet.multipart.max-file-size=2MB

# Security Configuration Variables
spring.security.filter.order=2

# Google
google.service.api-key=${FSRV_GOOGLE_SERVICE_API_KEY:}

# Graphhopper
graphhopper.api-key=${GRAPHHOPPER_API_KEY:}

# AWS SNS config
sns.prefix-arn=${FSRV_SNS_PREFIX_ARN:}
sns.topic-arn=${FSRV_SNS_TOPIC_ARN:}
sns.environment=${FSRV_SNS_ENV:development}


# AWS S3
s3.url=${FSRV_S3_URL:}
s3.bucket-name=${FSRV_S3_BUCKET_NAME:}
s3.receipt-key-prefix-name=${FSRV_S3_RECEIPT_KEY_PREFIX_NAME:receipt}
s3.signature-key-prefix-name=${FSRV_S3_SIGNATURE_KEY_PREFIX_NAME:signature}
s3.attachment-key-prefix-name=${FSRV_S3_ATTACHMENT_KEY_PREFIX_NAME:attachment}
s3.packaging-key-prefix-name=${FSRV_S3_PACKAGING_KEY_PREFIX_NAME:packaging}
s3.cdn-url=${FSRV_S3_CDN_URL:}

# Cache
spring.cache.cache-names=default,expireAfterAccess10m
spring.cache.caffeine.spec=expireAfterWrite=3600s,expireAfterAccess=600s

# Grab Express
GRAB_API_URL=${FSRV_GRAB_API_URL_KEY:}
ID_GRAB_PARTNER_ID=${FSRV_ID_GRAB_PARTNER_ID_KEY:}
ID_GRAB_PARTNER_SECRET=${FSRV_ID_GRAB_PARTNER_SECRET_KEY:}
TH_GRAB_PARTNER_ID=${FSRV_TH_GRAB_PARTNER_ID_KEY:}
TH_GRAB_PARTNER_SECRET=${FSRV_TH_GRAB_PARTNER_SECRET_KEY:}
MY_GRAB_PARTNER_ID=${FSRV_MY_GRAB_PARTNER_ID_KEY:}
MY_GRAB_PARTNER_SECRET=${FSRV_MY_GRAB_PARTNER_SECRET_KEY:}

# Segment
segment.analytics.android-write-key:${SEGMENT_ANDROID_WRITE_KEY:}
segment.analytics.ios-write-key:${SEGMENT_IOS_WRITE_KEY:}
segment.analytics.web-write-key:${SEGMENT_WEB_WRITE_KEY:}
segment.analytics.mobileweb-write-key:${SEGMENT_MOBILEWEB_WRITE_KEY:}
segment.analytics.grabfresh-write-key:${SEGMENT_GRABFRESH_WRITE_KEY:}
segment.analytics.sprinkles-write-key:${SEGMENT_SPRINKLES_WRITE_KEY:}

# Active MQ
spring.activemq.broker-url=${FSRV_ACTIVEMQ_BROKER_URL:}
spring.activemq.user=${FSRV_ACTIVEMQ_USER:}
spring.activemq.password=${FSRV_ACTIVEMQ_PASSWORD:}
activemq.environment=${FSRV_ACTIVEMQ_ENVIRONMENT:}

# Kafka
spring.kafka.producer.bootstrap-servers=${FSRV_KAFKA_PRODUCER_SERVER_URL:localhost:9092}
spring.kafka.consumer.bootstrap-servers=${FSRV_KAFKA_SERVER_URL:localhost:9092}
spring.kafka.consumer.group-id=${FSRV_KAFKA_CONSUMER_GROUP:internal_processing}
kafka.consumer.jubelio-group-id=${FSRV_KAFKA_JUBELIO_CONSUMER_GROUP:jubelio_processing_local}
kafka.consumer.resi-group-id=${FSRV_KAFKA_RESI_CONSUMER_GROUP:resi_processing_local}
kafka.consumer.slot-optimization-group-id=${FSRV_KAFKA_SLOT_OPTIMIZATION_CONSUMER_GROUP:slot_optimization_processing_local}
kafka.consumer.slot-optimization-tomorrow-group-id=${FSRV_KAFKA_SLOT_OPTIMIZATION_TOMORROW_CONSUMER_GROUP:slot_optimization_tomorrow_processing}
kafka.consumer.strato-group-id=${FSRV_KAFKA_STRATO_CONSUMER_GROUP:strato_processing_local}
kafka.consumer.enabler-group-id=${FSRV_KAFKA_ENABLER_CONSUMER_GROUP:enabler_processing_local}
kafka.consumer.payment-group-id=${FSRV_KAFKA_PAYMENT_CONSUMER_GROUP:fulfillment_payment_processing}
kafka.consumer.radar-group-id=${FSRV_KAFKA_RADAR_CONSUMER_GROUP:fulfillment_radar_processing}
kafka.topic.payment-event-topic=${FSRV_KAFKA_PAYMENT_EVENT_TOPIC:happyfresh_payment_sandbox}
kafka.consumer.spree-group-id=${FSRV_KAFKA_SPREE_CONSUMER_GROUP:fulfillment_spree_processing}
kafka.topic.user-event-topic=${FSRV_KAFKA_USER_EVENT_TOPIC:happyfresh_user}
kafka.consumer.delyva-group-id=${FSRV_KAFKA_DELYVA_CONSUMER_GROUP:fulfillment_delyva_processing}
kafka.authentication.username=${FSRV_KAFKA_USERNAME:}
kafka.authentication.password=${FSRV_KAFKA_PASSWORD:}
kafka.provider=${FSRV_KAFKA_PROVIDER:}
kafka.producer.authentication.username=${FSRV_KAFKA_PRODUCER_USERNAME:}
kafka.producer.authentication.password=${FSRV_KAFKA_PRODUCER_PASSWORD:}
kafka.producer.provider=${FSRV_KAFKA_PRODUCER_PROVIDER:}

# Braze
BRAZE_API_URL=${FSRV_BRAZE_API_URL_KEY:}
BRAZE_API_KEY=${FSRV_BRAZE_API_KEY:}
BRAZE_CAMPAIGN_KEY=${FSRV_BRAZE_CAMPAIGN_KEY:}

# API Documentation
API_DOCUMENTATION_USERNAME=${FSRV_API_DOCUMENTATION_USERNAME:admin}
API_DOCUMENTATION_PASSWORD=${FSRV_API_DOCUMENTATION_PASSWORD:admin}

# AES CBC-128 symmetric encryption
encryption.secret-key=${FSRV_ENCRYPTION_SECRET_KEY:}
encryption.initialization-vector=${FSRV_ENCRYPTION_INITIALIZATION_VECTOR:}

# ResiId credentials
resi.email=${FSRV_RESI_EMAIL:}
resi.password=${FSRV_RESI_PASSWORD:}
resi.apiKey=${FSRV_RESI_API_KEY:}

# Jubelio
jubelio.source-id=${FSRV_JUBELIO_SOURCE_ID:}

# Locus
locus.client-id=${FSRV_LOCUS_CLIENT_ID:}
locus.api-key=${FSRV_LOCUS_API_KEY:}
locus.base-url=${FSRV_LOCUS_BASE_URL:}

# Lalamove
lalamove.base-url=${FSRV_LALAMOVE_BASE_URL:}
lalamove.api-keys.id=${FSRV_LALAMOVE_ID_API_KEY:}
lalamove.api-keys.my=${FSRV_LALAMOVE_MY_API_KEY:}
lalamove.api-keys.th=${FSRV_LALAMOVE_TH_API_KEY:}
lalamove.api-secrets.id=${FSRV_LALAMOVE_ID_API_SECRET:}
lalamove.api-secrets.my=${FSRV_LALAMOVE_MY_API_SECRET:}
lalamove.api-secrets.th=${FSRV_LALAMOVE_TH_API_SECRET:}
lalamove.updater-delay=${FSRV_LALAMOVE_UPDATER_DELAY:5}
lalamove.retry-timeout-offset=${FSRV_LALAMOVE_TIMEOUT_OFFSET:10}
lalamove.max-try-count=${FSRV_LALAMOVE_MAX_TRY_COUNT:3}

# Login Attempt
login-attempt.max-attempt=${FSRV_LOGIN_ATTEMPT_MAX:5}
login-attempt.decay-in-seconds=${FSRV_LOGIN_ATTEMPT_DECAY:600}
login-attempt.lockout-duration-in-seconds=${FSRV_LOGIN_ATTEMPT_LOCKOUT_DURATION:900}

# Hypertrack
hypertrack.secret=${FSRV_HYPERTRACK_SECRET_KEY:secret}

# Strato
strato.base-url=${FSRV_STRATO_BASE_URL:}
strato.max-attempt=${FSRV_STRATO_MAX_ATTEMPT:3}
strato.backoff-ms=${FSRV_STRATO_BACKOFF:500}

# Radar
radar.base-url=${FSRV_RADAR_BASE_URL:}
radar.secret-key=${FSRV_RADAR_SECRET_KEY:}
radar.webhook-token=${FSRV_RADAR_WEBHOOK_TOKEN}

#As Worker
# Disable Kafka Listener
kafka.listener.enabled=${FSRV_KAFKA_LISTENER_ENABLED:true}
# Disable Active MQ Consumer
jms.listener.enabled=${FSRV_JMS_LISTENER_ENABLED:true}
# Disable Scheduler
scheduler.enabled=${FSRV_SCHEDULER_ENABLED:true}

# Delyva
delyva.base-url=${FSRV_DELYVA_BASE_URL:}
delyva.api-keys.my=${FSRV_DELYVA_MY_API_KEY:}
delyva.customer-ids.my=${FSRV_DELYVA_MY_CUSTOMER_ID:}
delyva.updater-delay=${FSRV_DELYVA_UPDATER_DELAY:5}
delyva.retry-timeout-offset=${FSRV_DELYVA_TIMEOUT_OFFSET:10}
delyva.max-try-count=${FSRV_DELYVA_MAX_TRY_COUNT:3}
delyva.api-secret=${FSRV_DELYVA_API_SECRET:}

# Hypermart
hypermart.base-url=${FSRV_HYPERMART_BASE_URL:}
hypermart.user=${FSRV_HYPERMART_USER:}
hypermart.password=${FSRV_HYPERMART_PASSWORD:}
hypermart.updater-delay=${FSRV_HYPERMART_UPDATER_DELAY:5}
hypermart.retry-timeout-offset=${FSRV_HYPERMART_TIMEOUT_OFFSET:10}
hypermart.max-try-count=${FSRV_HYPERMART_MAX_TRY_COUNT:3}

# MoEngage
moengage.push-max-recipient=${FSRV_MOENGAGE_PUSH_MAX_RECIPIENT:50}

#Coralogix
coralogix.endpoint-url=${FSRV_CORALOGIX_URL:}
coralogix.private-key=${FSRV_CORALOGIX_KEY:}
coralogix.application-name=${FSRV_CORALOGIX_APPNAME:}
coralogix.subsystem-prefix=${FSRV_CORALOGIX_SUBSYSTEM_PREFIX:}

# LezCash
lezcash.base-url=${FSRV_LEZCASH_BASE_URL:}
lezcash.api-key=${FSRV_LEZCASH_API_KEY:}
lezcash.error-code-above-to-enable-retry=${FSRV_LEZCASH_ERROR_CODE_ABOVE_TO_ENABLE_RETRY:500}
lezcash.enable-debugger=${FSRV_LEZCASH_ENABLE_DEBUGGER:false}

# App
app.min-snd-version-code-to-take-job=${FSRV_MIN_SND_VERSION_CODE_TO_TAKE_JOB:424}

# jobrunr - sidekiq alike
org.jobrunr.job-scheduler.enabled=${FSRV_KAFKA_LISTENER_ENABLED:true}
org.jobrunr.background-job-server.enabled=${FSRV_KAFKA_LISTENER_ENABLED:true}
org.jobrunr.dashboard.enabled=${FSRV_KAFKA_LISTENER_ENABLED:true}
org.jobrunr.background-job-server.worker-count=2

# Gosend
gosend.base-url=${FSRV_GOSEND_BASE_URL:}
gosend.cliend-id=${FSRV_GOSEND_CLIENT_ID:}
gosend.passkey=${FSRV_GOSEND_PASSKEY:}
gosend.webhook-token=${FSRV_GOSEND_WEBHOOK_TOKEN:}


# Oy
oy.base-url=${FSRV_OY_BASE_URL:}
oy.api-key=${FSRV_OY_API_KEY:}
oy.username=${FSRV_OY_USERNAME:}
oy.public-key=${FSRV_OY_PUBLIC_KEY:}