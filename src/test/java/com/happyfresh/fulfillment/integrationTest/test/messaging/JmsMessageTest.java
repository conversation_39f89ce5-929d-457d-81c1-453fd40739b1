package com.happyfresh.fulfillment.integrationTest.test.messaging;

import com.happyfresh.fulfillment.common.messaging.activemq.JmsMessage;
import com.happyfresh.fulfillment.common.messaging.activemq.Sender;
import com.happyfresh.fulfillment.common.messaging.activemq.type.MessageDestination;
import com.happyfresh.fulfillment.common.util.AutowireUtil;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.jms.core.MessageCreator;

import javax.jms.Message;
import javax.jms.Session;

import static org.mockito.Mockito.*;

public class JmsMessageTest extends BaseTest {

    @SpyBean
    private Sender sender;

    @Autowired
    private Environment environment;

    private String prefix;

    @Before
    public void setup() {
        prefix = "fulfillment." + environment.getProperty("activemq.environment") + ".";
    }

    @Test
    public void shouldSendCorrectDestinationMessage() throws Exception {
        JmsMessage hypertrack = JmsMessage.instance(MessageDestination.HYPERTRACK_SYNC, "hypertrack message")
            .withSenderType(Sender.Type.QUEUE)
            .withScheduledDelayInMillis(1000);
        JmsMessage lalamove = JmsMessage.instance(MessageDestination.LALAMOVE_DELIVERY, "lalamove message")
            .withSenderType(Sender.Type.QUEUE)
            .withScheduledDelayInMillis(1000);

        ArgumentCaptor<String> hTDestinationCaptor = ArgumentCaptor.forClass(String.class);
        hypertrack.send();
        verify(sender, times(1))
            .send(any(Sender.Type.class), hTDestinationCaptor.capture(), any(MessageCreator.class));
        Assert.assertEquals(prefix + MessageDestination.HYPERTRACK_SYNC, hTDestinationCaptor.getValue());

        JmsMessage other = JmsMessage.instance(MessageDestination.GRAB_BOOKING, "ge message")
            .withSenderType(Sender.Type.QUEUE);

        ArgumentCaptor<String> llmDestinationCaptor = ArgumentCaptor.forClass(String.class);
        lalamove.send();
        verify(sender, times(2))
            .send(any(Sender.Type.class), llmDestinationCaptor.capture(), any(MessageCreator.class));
        Assert.assertEquals(prefix + MessageDestination.LALAMOVE_DELIVERY, llmDestinationCaptor.getValue());
    }
}
