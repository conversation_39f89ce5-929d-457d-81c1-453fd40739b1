import java.time.Instant

buildscript {
    ext {
        springBootVersion = '2.1.3.RELEASE'
    }
    ext['httpcore.version'] = '4.4.6'
    ext['log4j2.version'] = '2.17.0'

    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }

}

plugins {
    id 'net.ltgt.apt' version '0.15'
    id "fi.evident.beanstalk" version "0.1.3"
    id "org.sonarqube" version "2.7.1"
    id "jacoco"
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply from: "gradle/deploy.gradle"
apply plugin: 'jacoco'


group = 'com.happyfresh.fulfillment'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = 11

repositories {
    mavenCentral()
    maven {
        url 'https://s3.amazonaws.com/download.elasticsearch.org/lucenesnapshots/00142c9'
    }
}

configurations {
    compile.exclude module: 'spring-boot-starter-logging'
}

dependencies {
    compile('org.springframework.boot:spring-boot-starter-actuator')
    compile('org.springframework.boot:spring-boot-starter-cache')
    compile('org.springframework.boot:spring-boot-starter-data-jpa')
    compile('org.springframework.boot:spring-boot-starter-security')
    compile('org.springframework.boot:spring-boot-starter-web-services') {
        exclude module: 'spring-boot-starter-tomcat'
    }
    compile('org.springframework.boot:spring-boot-starter-undertow') {
        exclude group: 'io.undertow', module: 'undertow-websockets-jsr'
    }
    compile('org.springframework.boot:spring-boot-starter-log4j2')
    compile('org.springframework.boot:spring-boot-starter-cache')
    compile('org.springframework.boot:spring-boot-starter-activemq:2.1.3.RELEASE')
    compile('org.springframework.kafka:spring-kafka')

    compile('io.searchbox:jest:5.3.4')
    compile('org.elasticsearch:elasticsearch:5.5.1')
    compile('org.mapstruct:mapstruct-processor:1.3.0.Final')
    compile('org.mapstruct:mapstruct-jdk8:1.3.0.Final')
    compile('org.freemarker:freemarker:2.3.28')
    compile('io.logz.log4j2:logzio-log4j2-appender:1.0.11')
    compile('com.google.guava:guava:27.1-jre')
    compile('io.sentry:sentry-log4j2:5.5.2')
    compile('redis.clients:jedis:2.9.3')
    compile('org.flywaydb:flyway-core:5.2.4')
    compile('org.apache.commons:commons-lang3:3.8.1')
    compile('org.apache.commons:commons-csv:1.6')
    compile('com.amazonaws:aws-java-sdk-sns:1.12.262')
    compile('com.amazonaws:aws-java-sdk-s3:1.12.262')
    compile('org.postgresql:postgresql')
    compile('com.google.maps:google-maps-services:0.9.3')
    compile('com.graphhopper:jsprit-core:1.7.2')
    compile('com.github.ben-manes.caffeine:caffeine:2.7.0')
    compile('org.javers:javers-spring-boot-starter-sql:5.3.3')
    compile('com.segment.analytics.java:analytics:2.1.1')
    compile('com.graphhopper:directions-api-client-hc:1.0-pre3')
    compile('net.javacrumbs.shedlock:shedlock-spring:4.24.0')
    compile('net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.24.0')
    compile files('library/jedis-lock/jedis-lock.jar')
    implementation('com.googlecode.libphonenumber:libphonenumber:8.10.8')
    implementation('io.github.resilience4j:resilience4j-circuitbreaker:1.7.0')
    implementation('org.jetbrains.kotlin:kotlin-stdlib:1.3.70')
    implementation('com.fasterxml.jackson.core:jackson-databind:2.10.0')
    implementation('com.fasterxml.jackson.core:jackson-core:2.10.0')
    implementation('org.jobrunr:jobrunr-spring-boot-2-starter:7.2.2')

    compileOnly('org.projectlombok:lombok:1.18.6')

    annotationProcessor('org.mapstruct:mapstruct-processor:1.3.0.Final')
    annotationProcessor('org.projectlombok:lombok:1.18.6')

    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testImplementation('org.springframework.security:spring-security-test')
    testImplementation('org.powermock:powermock-module-junit4:2.0.9')
    testImplementation('org.powermock:powermock-api-mockito2:2.0.9')
}

task createMigrationFile {
    doLast {
        def defaultSuffix = "rename_this"
        def suffix = (project.hasProperty('f') && f?.trim()) ? f : defaultSuffix
        suffix = (project.hasProperty('c') && c?.trim()) ? "create_table_${c}" : suffix

        def ddl = (project.hasProperty('c') && c?.trim()) ? "CREATE TABLE hff_${c} (\n);" : "--Write the SQL script here"

        Instant instant = Instant.now()
        Long millisecond = instant.toEpochMilli()

        String path = "${projectDir}/src/main/resources/db/migration/"
        String filename = "v${millisecond}__${suffix}.sql"

        file(path + filename).text = ddl
        println "Done creating ${filename}"
    }
}

test {
    ignoreFailures = false
    maxHeapSize = "1024m"

    filter {
        includeTestsMatching "com.happyfresh.fulfillment.unit.*"
        includeTestsMatching "com.happyfresh.fulfillment.integrationTest.repositoryTest.*"
    }
}

jacocoTestReport {
    reports {
        xml.enabled true
    }
}

test.finalizedBy jacocoTestReport

bootJar {
    mainClassName = 'com.happyfresh.fulfillment.MainApp'
}

sonarqube {
    properties {
        property "sonar.tests", "src/test/java/com/happyfresh/fulfillment/unit"
    }
}