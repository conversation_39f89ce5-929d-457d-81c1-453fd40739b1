# Base Stage [COR1001] using official for gradle [COR1007]
FROM gradle:6.9.2-jdk11 AS base

# Shell option to fail pipes according to https://github.com/hadolint/hadolint/wiki/DL4006 recommendation [COR4001]
SHELL ["/bin/bash", "-o", "pipefail", "-c"]
RUN mkdir -p /home/<USER>/cache_home
ENV GRADLE_USER_HOME /home/<USER>/cache_home

# Dev Stage [COR1002]
FROM base as dev
COPY build.gradle gradlew /opt/app/
COPY gradle /opt/app/gradle
WORKDIR /opt/app
RUN echo "org.gradle.jvmargs=-Xmx2048M" > gradle.properties
RUN gradle clean build --build-cache -i --stacktrace

# Pre-Prod Stage [COR1004]
FROM dev as pre-prod
COPY . .
RUN gradle build  -x test -i --stacktrace --build-cache


# Prod Stage [COR1005] and minimal OS version [COR1006]
FROM openjdk:11-jre-slim-bullseye AS prod

ARG VERSION
ARG DD_ENV
# Datadog
ENV DD_ENV=$DD_ENV
ENV DD_VERSION=$VERSION
LABEL com.datadoghq.tags.version=$VERSION

RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create a restricted user for use in production server [COR4002]
RUN groupadd -r appuser
RUN useradd -r -s /bin/false -g appuser appuser

# Temporary, while log not to stdout
RUN groupadd -r loggroup
RUN usermod -aG loggroup root
RUN usermod -aG loggroup appuser
RUN chown -R root:loggroup /var/log
RUN chmod -R g+rwX /var/log

WORKDIR /opt/app
COPY --from=pre-prod /opt/app/build/libs/app-0.0.1-SNAPSHOT.jar .
COPY --from=pre-prod /opt/app/datadog/dd-java-agent.jar .
COPY --from=pre-prod /opt/app/signoz/opentelemetry-javaagent.jar .
COPY --from=pre-prod /opt/app/entrypoint.sh .
RUN chmod +x entrypoint.sh

RUN chown -R appuser:appuser /opt/app
USER appuser

EXPOSE 8080
# Use a Healthcheck [COR3003]
HEALTHCHECK --interval=15s --timeout=5s --retries=2 --start-period=10s CMD curl -f 0.0.0.0:8080/actuator/health || exit 1

ENTRYPOINT ["/opt/app/entrypoint.sh"]
